<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Admin User
        User::create([
            'name' => 'Admin TikPro',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'phone' => '081234567890',
            'role' => User::ROLE_ADMIN,
            'email_verified_at' => now(),
            'is_active' => true,
        ]);

        // Create Staff User
        User::create([
            'name' => 'Staff TikPro',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'phone' => '081234567891',
            'role' => User::ROLE_STAFF,
            'email_verified_at' => now(),
            'is_active' => true,
        ]);

        // Create Penjual User
        User::create([
            'name' => 'Event Organizer',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'phone' => '081234567892',
            'role' => User::ROLE_PENJUAL,
            'email_verified_at' => now(),
            'is_active' => true,
        ]);

        // Create Pembeli User
        User::create([
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'phone' => '081234567893',
            'role' => User::ROLE_PEMBELI,
            'email_verified_at' => now(),
            'is_active' => true,
        ]);

        // Create additional test users
        User::factory(10)->create([
            'role' => User::ROLE_PEMBELI,
            'email_verified_at' => now(),
            'is_active' => true,
        ]);

        // Create additional organizers
        User::factory(3)->create([
            'role' => User::ROLE_PENJUAL,
            'email_verified_at' => now(),
            'is_active' => true,
        ]);
    }
}
