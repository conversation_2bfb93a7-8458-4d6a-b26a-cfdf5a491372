<?php

namespace App\Policies;

use App\Models\Event;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class EventPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any events.
     */
    public function viewAny(User $user): bool
    {
        return true; // All authenticated users can view events
    }

    /**
     * Determine whether the user can view the event.
     */
    public function view(User $user, Event $event): bool
    {
        // Admin can view all events
        if ($user->isAdmin()) {
            return true;
        }

        // Staff can view all events
        if ($user->isStaff()) {
            return true;
        }

        // Organizer can view their own events
        if ($user->isPenjual() && $event->organizer_id === $user->id) {
            return true;
        }

        // Regular users can only view published events
        return $event->status === 'published';
    }

    /**
     * Determine whether the user can create events.
     */
    public function create(User $user): bool
    {
        // Only organizers and admins can create events
        return $user->isPenjual() || $user->isAdmin();
    }

    /**
     * Determine whether the user can update the event.
     */
    public function update(User $user, Event $event): bool
    {
        // Admin can update all events
        if ($user->isAdmin()) {
            return true;
        }

        // Organizer can only update their own events
        if ($user->isPenjual() && $event->organizer_id === $user->id) {
            // Cannot update if event has started
            if ($event->hasStarted()) {
                return false;
            }

            // Cannot update if event has sold tickets (with some exceptions)
            if ($event->tickets()->count() > 0) {
                // Only allow certain fields to be updated
                return false;
            }

            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can delete the event.
     */
    public function delete(User $user, Event $event): bool
    {
        // Admin can delete all events
        if ($user->isAdmin()) {
            return true;
        }

        // Organizer can only delete their own events
        if ($user->isPenjual() && $event->organizer_id === $user->id) {
            // Cannot delete if event has started
            if ($event->hasStarted()) {
                return false;
            }

            // Cannot delete if event has sold tickets
            if ($event->tickets()->count() > 0) {
                return false;
            }

            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can publish the event.
     */
    public function publish(User $user, Event $event): bool
    {
        // Admin can publish all events
        if ($user->isAdmin()) {
            return true;
        }

        // Organizer can only publish their own events
        if ($user->isPenjual() && $event->organizer_id === $user->id) {
            // Event must be in draft status
            return $event->status === 'draft';
        }

        return false;
    }

    /**
     * Determine whether the user can unpublish the event.
     */
    public function unpublish(User $user, Event $event): bool
    {
        // Admin can unpublish all events
        if ($user->isAdmin()) {
            return true;
        }

        // Organizer can only unpublish their own events
        if ($user->isPenjual() && $event->organizer_id === $user->id) {
            // Event must be published
            if ($event->status !== 'published') {
                return false;
            }

            // Cannot unpublish if event has started
            if ($event->hasStarted()) {
                return false;
            }

            // Cannot unpublish if event has sold tickets
            if ($event->tickets()->count() > 0) {
                return false;
            }

            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can manage tickets for the event.
     */
    public function manageTickets(User $user, Event $event): bool
    {
        // Admin can manage all event tickets
        if ($user->isAdmin()) {
            return true;
        }

        // Staff can manage all event tickets
        if ($user->isStaff()) {
            return true;
        }

        // Organizer can manage their own event tickets
        if ($user->isPenjual() && $event->organizer_id === $user->id) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can view event analytics.
     */
    public function viewAnalytics(User $user, Event $event): bool
    {
        // Admin can view all event analytics
        if ($user->isAdmin()) {
            return true;
        }

        // Organizer can view their own event analytics
        if ($user->isPenjual() && $event->organizer_id === $user->id) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can duplicate the event.
     */
    public function duplicate(User $user, Event $event): bool
    {
        // Admin can duplicate all events
        if ($user->isAdmin()) {
            return true;
        }

        // Organizer can duplicate their own events
        if ($user->isPenjual() && $event->organizer_id === $user->id) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can feature the event.
     */
    public function feature(User $user, Event $event): bool
    {
        // Only admin can feature events
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can cancel the event.
     */
    public function cancel(User $user, Event $event): bool
    {
        // Admin can cancel all events
        if ($user->isAdmin()) {
            return true;
        }

        // Organizer can cancel their own events
        if ($user->isPenjual() && $event->organizer_id === $user->id) {
            // Cannot cancel if event has already started
            if ($event->hasStarted()) {
                return false;
            }

            // Event must be published
            return $event->status === 'published';
        }

        return false;
    }

    /**
     * Determine whether the user can restore the event.
     */
    public function restore(User $user, Event $event): bool
    {
        // Only admin can restore events
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can permanently delete the event.
     */
    public function forceDelete(User $user, Event $event): bool
    {
        // Only admin can force delete events
        return $user->isAdmin();
    }
}
