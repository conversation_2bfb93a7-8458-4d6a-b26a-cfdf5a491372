<?php

namespace App\Http\Controllers;

use App\Models\Event;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class HomeController extends Controller
{
    /**
     * Show the home page
     */
    public function index()
    {
        // Cache data for better performance
        $categories = Cache::remember('categories', 3600, function () {
            return Category::where('is_active', true)
                          ->orderBy('name')
                          ->get();
        });

        $featuredEvents = Cache::remember('featured_events', 1800, function () {
            return Event::with(['category', 'organizer'])
                       ->where('status', 'published')
                       ->where('is_featured', true)
                       ->where('start_date', '>', now())
                       ->orderBy('start_date')
                       ->limit(6)
                       ->get();
        });

        $latestEvents = Cache::remember('latest_events', 1800, function () {
            return Event::with(['category', 'organizer'])
                       ->where('status', 'published')
                       ->where('start_date', '>', now())
                       ->orderBy('created_at', 'desc')
                       ->limit(6)
                       ->get();
        });

        $popularEvents = Cache::remember('popular_events', 1800, function () {
            return Event::with(['category', 'organizer'])
                       ->where('status', 'published')
                       ->where('start_date', '>', now())
                       ->withCount('orders')
                       ->orderBy('orders_count', 'desc')
                       ->limit(6)
                       ->get();
        });

        // Get all events for the main grid (with pagination)
        $events = Event::with(['category', 'organizer'])
                      ->where('status', 'published')
                      ->where('start_date', '>', now())
                      ->orderBy('start_date')
                      ->paginate(12);

        return view('pages.home', [
            'title' => 'Beranda',
            'categories' => $categories,
            'featuredEvents' => $featuredEvents,
            'latestEvents' => $latestEvents,
            'popularEvents' => $popularEvents,
            'events' => $events,
        ]);
    }

    /**
     * Get featured events for AJAX
     */
    public function featuredEvents()
    {
        $events = Event::with(['category', 'organizer'])
                      ->where('status', 'published')
                      ->where('is_featured', true)
                      ->where('start_date', '>', now())
                      ->orderBy('start_date')
                      ->limit(6)
                      ->get()
                      ->map(function ($event) {
                          return [
                              'id' => $event->id,
                              'title' => $event->title,
                              'poster' => $event->poster_url,
                              'category' => $event->category->name,
                              'start_date' => $event->start_date->format('d M Y'),
                              'venue_name' => $event->venue_name,
                              'city' => $event->city,
                              'price' => $event->formatted_price,
                              'url' => route('events.show', $event),
                              'is_free' => $event->price == 0,
                              'available_tickets' => $event->available_capacity,
                          ];
                      });

        return response()->json($events);
    }

    /**
     * Search events
     */
    public function search(Request $request)
    {
        $query = $request->get('q', '');
        $category = $request->get('category');
        $city = $request->get('city');
        $date = $request->get('date');
        $price_min = $request->get('price_min');
        $price_max = $request->get('price_max');

        $events = Event::with(['category', 'organizer'])
                      ->where('status', 'published')
                      ->where('start_date', '>', now());

        // Search by title or description
        if ($query) {
            $events->where(function ($q) use ($query) {
                $q->where('title', 'like', "%{$query}%")
                  ->orWhere('description', 'like', "%{$query}%")
                  ->orWhere('venue_name', 'like', "%{$query}%");
            });
        }

        // Filter by category
        if ($category) {
            $events->where('category_id', $category);
        }

        // Filter by city
        if ($city) {
            $events->where('city', 'like', "%{$city}%");
        }

        // Filter by date
        if ($date) {
            $events->whereDate('start_date', $date);
        }

        // Filter by price range
        if ($price_min) {
            $events->where('price', '>=', $price_min);
        }

        if ($price_max) {
            $events->where('price', '<=', $price_max);
        }

        $events = $events->orderBy('start_date')->paginate(12);

        $categories = Category::where('is_active', true)->orderBy('name')->get();

        return view('pages.search', [
            'title' => 'Pencarian Event',
            'events' => $events,
            'categories' => $categories,
            'query' => $query,
            'filters' => [
                'category' => $category,
                'city' => $city,
                'date' => $date,
                'price_min' => $price_min,
                'price_max' => $price_max,
            ]
        ]);
    }

    /**
     * Get events by category
     */
    public function eventsByCategory(Category $category)
    {
        $events = Event::with(['category', 'organizer'])
                      ->where('category_id', $category->id)
                      ->where('status', 'published')
                      ->where('start_date', '>', now())
                      ->orderBy('start_date')
                      ->paginate(12);

        return view('pages.category', [
            'title' => "Event {$category->name}",
            'category' => $category,
            'events' => $events,
        ]);
    }

    /**
     * Get nearby events based on user location
     */
    public function nearbyEvents(Request $request)
    {
        $latitude = $request->get('lat');
        $longitude = $request->get('lng');
        $radius = $request->get('radius', 50); // Default 50km

        if (!$latitude || !$longitude) {
            return response()->json(['error' => 'Location required'], 400);
        }

        // Using Haversine formula to calculate distance
        $events = Event::with(['category', 'organizer'])
                      ->where('status', 'published')
                      ->where('start_date', '>', now())
                      ->whereNotNull('latitude')
                      ->whereNotNull('longitude')
                      ->selectRaw("
                          *,
                          (6371 * acos(cos(radians(?))
                          * cos(radians(latitude))
                          * cos(radians(longitude) - radians(?))
                          + sin(radians(?))
                          * sin(radians(latitude)))) AS distance
                      ", [$latitude, $longitude, $latitude])
                      ->having('distance', '<', $radius)
                      ->orderBy('distance')
                      ->limit(20)
                      ->get();

        return response()->json($events->map(function ($event) {
            return [
                'id' => $event->id,
                'title' => $event->title,
                'poster' => $event->poster_url,
                'category' => $event->category->name,
                'start_date' => $event->start_date->format('d M Y'),
                'venue_name' => $event->venue_name,
                'city' => $event->city,
                'price' => $event->formatted_price,
                'url' => route('events.show', $event),
                'distance' => round($event->distance, 1),
            ];
        }));
    }
}
