@extends('layouts.app')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- PWA Install Prompt -->
    @if($showInstallPrompt)
    <div class="bg-primary/10 p-4 rounded-xl flex items-center justify-between mb-6" id="pwa-install-prompt" data-aos="fade-down">
        <div class="flex items-center space-x-3">
            <svg class="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"/>
            </svg>
            <span class="text-sm">Install TikPro untuk akses tiket offline</span>
        </div>
        <button onclick="installPWA()" class="px-4 py-2 bg-primary text-white rounded-lg text-sm hover:bg-primary/90 transition-colors">
            Install
        </button>
    </div>
    @endif

    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-8">
        <h1 class="text-2xl font-bold" data-aos="fade-up">Tiket Saya</h1>
        
        <div class="flex items-center space-x-2" data-aos="fade-up" data-aos-delay="100">
            <div class="relative" x-data="{ open: false }">
                <button @click="open = !open" class="px-4 py-2 bg-white text-gray-600 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors flex items-center space-x-2">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"/>
                    </svg>
                    <span>Filter</span>
                </button>

                <div x-show="open" @click.away="open = false" class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-100 py-1 z-10">
                    <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">Semua Tiket</a>
                    <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">Aktif</a>
                    <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">Terpakai</a>
                    <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">Kadaluarsa</a>
                </div>
            </div>

            <div class="relative" x-data="{ open: false }">
                <button @click="open = !open" class="px-4 py-2 bg-white text-gray-600 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors flex items-center space-x-2">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4h13M3 8h9m-9 4h6m4 0l4-4m0 0l4 4m-4-4v12"/>
                    </svg>
                    <span>Urutkan</span>
                </button>

                <div x-show="open" @click.away="open = false" class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-100 py-1 z-10">
                    <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">Terbaru</a>
                    <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">Terlama</a>
                    <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">Event Terdekat</a>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        @forelse($tickets as $ticket)
        <div class="bg-white rounded-xl shadow-lg overflow-hidden border border-gray-100 transform hover:scale-[1.02] transition-all duration-300" 
             data-aos="fade-up" 
             data-aos-delay="{{ $loop->iteration * 50 }}"
             x-data="{ showActions: false }"
             @mouseenter="showActions = true"
             @mouseleave="showActions = false">
            <!-- Event Image -->
            <div class="relative h-48">
                <img src="{{ $ticket->event->poster_url }}" 
                     alt="{{ $ticket->event->title }}" 
                     class="w-full h-full object-cover">
                <div class="absolute top-4 right-4">
                    <span class="px-3 py-1 rounded-full text-sm 
                        {{ $ticket->status === 'used' ? 'bg-gray-500 text-white' : 
                           ($ticket->status === 'active' ? 'bg-green-500 text-white' : 'bg-yellow-500 text-white') }}">
                        {{ ucfirst($ticket->status) }}
                    </span>
                </div>
            </div>

            <!-- Ticket Details -->
            <div class="p-4">
                <h3 class="font-semibold text-lg mb-2">{{ $ticket->event->title }}</h3>
                
                <div class="space-y-2 text-sm text-gray-600 mb-4">
                    <!-- Date & Time -->
                    <div class="flex items-center space-x-2">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                  d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                        </svg>
                        <span>{{ $ticket->event->start_date->format('d M Y - H:i') }}</span>
                    </div>

                    <!-- Location -->
                    <div class="flex items-center space-x-2">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                  d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                  d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                        </svg>
                        <span>{{ $ticket->event->location }}</span>
                    </div>

                    <!-- Ticket Number -->
                    <div class="flex items-center space-x-2">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                  d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"/>
                        </svg>
                        <span>Tiket #{{ $ticket->ticket_number }}</span>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="space-y-3">
                    <!-- Status Badge -->
                    <div class="flex items-center justify-between text-sm">
                        <span class="text-gray-600">Status</span>
                        <span class="px-3 py-1 rounded-full text-sm
                            {{ $ticket->status === 'used' ? 'bg-gray-100 text-gray-600' : 
                               ($ticket->status === 'active' ? 'bg-green-100 text-green-600' : 'bg-yellow-100 text-yellow-600') }}">
                            {{ ucfirst($ticket->status) }}
                        </span>
                    </div>

                    <!-- Offline Status -->
                    <div class="flex items-center justify-between text-sm">
                        <span class="text-gray-600">Offline Mode</span>
                        <span class="px-3 py-1 rounded-full text-sm {{ $ticket->is_cached ? 'bg-primary/10 text-primary' : 'bg-gray-100 text-gray-600' }}">
                            {{ $ticket->is_cached ? 'Tersedia Offline' : 'Perlu Internet' }}
                        </span>
                    </div>

                    <div class="flex space-x-2 pt-3 border-t border-gray-100">
                        <!-- View QR Code -->
                        <button onclick="showQRCode('{{ $ticket->qr_code }}')" 
                                class="flex-1 bg-primary text-white py-3 rounded-lg font-medium hover:bg-primary/90 transition-colors flex items-center justify-center space-x-2"
                                {{ $ticket->status === 'used' ? 'disabled' : '' }}>
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z"/>
                            </svg>
                            <span>Lihat QR Code</span>
                        </button>

                        <!-- Save for Offline -->
                        <button onclick="cacheTicketOffline('{{ $ticket->id }}')" 
                                class="flex items-center justify-center bg-gray-100 text-gray-700 py-3 px-4 rounded-lg hover:bg-gray-200 transition-colors"
                                title="Simpan untuk offline">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        @empty
        <div class="col-span-full text-center py-12" data-aos="fade-up">
            <svg class="w-16 h-16 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                      d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
            <p class="text-gray-500 mb-4">Kamu belum memiliki tiket</p>
            <a href="{{ route('home') }}" 
               class="inline-flex items-center text-primary hover:text-primary/80 transition-colors">
                <span>Jelajahi Event</span>
                <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"/>
                </svg>
            </a>
        </div>
        @endforelse
    </div>

    <!-- QR Code Modal -->
    <div id="qrModal" 
         class="fixed inset-0 bg-black/50 flex items-center justify-center hidden"
         onclick="this.classList.add('hidden')">
        <div class="bg-white p-6 rounded-xl max-w-sm w-full mx-4" 
             onclick="event.stopPropagation()">
            <div class="text-center">
                <h3 class="text-lg font-semibold mb-4">QR Code Tiket</h3>
                <div id="qrCodeContainer" class="mb-4"></div>
                <p class="text-sm text-gray-600 mb-4">Tunjukkan QR Code ini kepada petugas saat memasuki venue</p>
                <button onclick="document.getElementById('qrModal').classList.add('hidden')" 
                        class="w-full bg-primary text-white py-2 rounded-lg font-medium hover:bg-primary/90 transition-colors">
                    Tutup
                </button>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/qrcode@1.4.4/build/qrcode.min.js"></script>
<script>
    // PWA Installation
    let deferredPrompt;

    window.addEventListener('beforeinstallprompt', (e) => {
        e.preventDefault();
        deferredPrompt = e;
        showInstallPromotion();
    });

    function showInstallPromotion() {
        const prompt = document.getElementById('pwa-install-prompt');
        if (prompt) prompt.classList.remove('hidden');
    }

    async function installPWA() {
        if (!deferredPrompt) return;

        deferredPrompt.prompt();
        const { outcome } = await deferredPrompt.userChoice;
        
        if (outcome === 'accepted') {
            console.log('PWA installed successfully');
        }
        
        deferredPrompt = null;
        document.getElementById('pwa-install-prompt').classList.add('hidden');
    }

    // QR Code Generation
    function showQRCode(data) {
        const modal = document.getElementById('qrModal');
        const container = document.getElementById('qrCodeContainer');
        container.innerHTML = '';
        
        QRCode.toCanvas(container, data, {
            width: 200,
            height: 200,
            color: {
                dark: '#000000',
                light: '#ffffff'
            }
        });
        
        modal.classList.remove('hidden');
    }

    // Offline Caching
    async function cacheTicketOffline(ticketId) {
        if ('caches' in window) {
            try {
                const cache = await caches.open('tikpro-tickets');
                const ticketUrl = `/api/tickets/${ticketId}`;
                const response = await fetch(ticketUrl);
                
                if (response.ok) {
                    await cache.put(ticketUrl, response.clone());
                    
                    // Update UI to show ticket is available offline
                    const button = event.target.closest('button');
                    button.classList.add('bg-primary', 'text-white');
                    button.setAttribute('title', 'Tersedia offline');
                    
                    // Show success message
                    showNotification('Tiket berhasil disimpan untuk akses offline', 'success');
                }
            } catch (error) {
                console.error('Failed to cache ticket:', error);
                showNotification('Gagal menyimpan tiket untuk akses offline', 'error');
            }
        } else {
            showNotification('Browser Anda tidak mendukung penyimpanan offline', 'error');
        }
    }

    // Notification Helper
    function showNotification(message, type = 'success') {
        const notification = document.createElement('div');
        notification.className = `fixed bottom-4 right-4 px-6 py-3 rounded-lg shadow-lg transform transition-all duration-300 ${type === 'success' ? 'bg-green-500' : 'bg-red-500'} text-white`;
        notification.textContent = message;

        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => notification.classList.add('translate-y-0'), 100);
        notification.classList.add('translate-y-full');

        // Remove after 3 seconds
        setTimeout(() => {
            notification.classList.add('translate-y-full', 'opacity-0');
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }
</script>
@endpush
@endsection