@extends('layouts.app')

@section('content')
<div class="min-h-screen flex items-center justify-center px-4">
    <div class="max-w-md w-full text-center" data-aos="fade-up">
        <!-- Offline Illustration -->
        <div class="mb-8">
            <svg class="w-48 h-48 mx-auto text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                      d="M18.364 5.636a9 9 0 010 12.728m0 0l-2.829-2.829m2.829 2.829L21 21M15.536 8.464a5 5 0 010 7.072m0 0l-2.829-2.829m-4.243 2.829a4.978 4.978 0 01-1.414-2.83m-1.414 5.658a9 9 0 01-2.167-9.238m7.824 2.167a1 1 0 111.414 1.414m-1.414-1.414L3 3m8.293 8.293l1.414 1.414"/>
            </svg>
        </div>

        <h1 class="text-3xl font-bold mb-4">Tidak Ada Koneksi Internet</h1>
        <p class="text-gray-600 mb-8">
            Sepertinya kamu sedang offline. Tidak masalah, kamu masih bisa melihat tiket yang sudah dibeli.
        </p>

        <!-- Offline Actions -->
        <div class="space-y-4">
            <!-- View Cached Tickets -->
            <a href="{{ route('tickets.index') }}" 
               class="block w-full bg-primary text-white py-3 rounded-lg font-medium hover:bg-primary/90 transition-colors">
                Lihat Tiket Saya
            </a>

            <!-- Retry Connection -->
            <button onclick="window.location.reload()" 
                    class="block w-full bg-white border border-gray-300 text-gray-700 py-3 rounded-lg font-medium hover:bg-gray-50 transition-colors">
                Coba Lagi
            </button>
        </div>

        <!-- Offline Features Info -->
        <div class="mt-12">
            <h2 class="font-semibold mb-4">Fitur yang Tersedia Saat Offline:</h2>
            <div class="space-y-3 text-left">
                <div class="flex items-start space-x-3">
                    <svg class="w-5 h-5 text-primary flex-shrink-0 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                    </svg>
                    <span class="text-gray-600">Melihat tiket yang sudah dibeli</span>
                </div>
                <div class="flex items-start space-x-3">
                    <svg class="w-5 h-5 text-primary flex-shrink-0 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                    </svg>
                    <span class="text-gray-600">Menampilkan QR Code tiket</span>
                </div>
                <div class="flex items-start space-x-3">
                    <svg class="w-5 h-5 text-primary flex-shrink-0 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                    </svg>
                    <span class="text-gray-600">Melihat detail event yang tiketnya sudah dibeli</span>
                </div>
            </div>
        </div>

        <!-- PWA Install Prompt (if not installed) -->
        <div id="pwaInstall" class="hidden mt-8 p-4 bg-gray-50 rounded-lg">
            <p class="text-sm text-gray-600 mb-3">
                Install aplikasi TikPro untuk akses lebih mudah dan fitur offline
            </p>
            <button id="installButton" 
                    class="w-full bg-primary text-white py-2 rounded-lg font-medium hover:bg-primary/90 transition-colors">
                Install Aplikasi
            </button>
        </div>
    </div>
</div>

@push('scripts')
<script>
    // Check if the app can be installed
    let deferredPrompt;

    window.addEventListener('beforeinstallprompt', (e) => {
        e.preventDefault();
        deferredPrompt = e;
        document.getElementById('pwaInstall').classList.remove('hidden');
    });

    document.getElementById('installButton').addEventListener('click', async () => {
        if (deferredPrompt) {
            deferredPrompt.prompt();
            const { outcome } = await deferredPrompt.userChoice;
            if (outcome === 'accepted') {
                document.getElementById('pwaInstall').classList.add('hidden');
            }
            deferredPrompt = null;
        }
    });
</script>
@endpush
@endsection