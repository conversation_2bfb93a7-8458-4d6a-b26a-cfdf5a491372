<?php

namespace App\Http\Livewire;

use Livewire\Component;
use App\Models\Event;

class TicketPurchase extends Component
{
    public Event $event;
    public int $quantity = 1;
    public float $totalPrice = 0;

    protected $rules = [
        'quantity' => 'required|integer|min:1',
    ];

    public function mount(Event $event)
    {
        $this->event = $event;
        $this->calculateTotal();
    }

    public function incrementQuantity()
    {
        if ($this->quantity < $this->event->available_tickets) {
            $this->quantity++;
            $this->calculateTotal();
        }
    }

    public function decrementQuantity()
    {
        if ($this->quantity > 1) {
            $this->quantity--;
            $this->calculateTotal();
        }
    }

    public function calculateTotal()
    {
        $this->totalPrice = $this->event->price * $this->quantity;
    }

    public function addToCart()
    {
        $this->validate();

        if ($this->quantity > $this->event->available_tickets) {
            $this->addError('quantity', 'Jumlah tiket melebihi stok yang tersedia.');
            return;
        }

        // Simpan ke cart atau lanjut ke pembayaran
        $this->emit('ticketAddedToCart', [
            'event_id' => $this->event->id,
            'quantity' => $this->quantity,
            'total_price' => $this->totalPrice
        ]);

        session()->flash('message', 'Tiket berhasil ditambahkan ke keranjang!');
    }

    public function render()
    {
        return view('livewire.ticket-purchase');
    }
}