<!-- Notification Toast System -->
<div x-data="notificationSystem()" 
     @show-notification.window="showNotification($event.detail)"
     class="fixed top-4 right-4 z-50 space-y-2">
    
    <!-- Notification Container -->
    <template x-for="notification in notifications" :key="notification.id">
        <div x-show="notification.show" 
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0 transform translate-x-full scale-95"
             x-transition:enter-end="opacity-100 transform translate-x-0 scale-100"
             x-transition:leave="transition ease-in duration-200"
             x-transition:leave-start="opacity-100 transform translate-x-0 scale-100"
             x-transition:leave-end="opacity-0 transform translate-x-full scale-95"
             class="max-w-sm w-full bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden"
             :class="{
                'border-green-200 bg-green-50': notification.type === 'success',
                'border-red-200 bg-red-50': notification.type === 'error',
                'border-yellow-200 bg-yellow-50': notification.type === 'warning',
                'border-blue-200 bg-blue-50': notification.type === 'info'
             }">
            
            <div class="p-4">
                <div class="flex items-start">
                    <!-- Icon -->
                    <div class="flex-shrink-0">
                        <!-- Success Icon -->
                        <div x-show="notification.type === 'success'" 
                             class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                        </div>
                        
                        <!-- Error Icon -->
                        <div x-show="notification.type === 'error'" 
                             class="w-6 h-6 bg-red-100 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                            </svg>
                        </div>
                        
                        <!-- Warning Icon -->
                        <div x-show="notification.type === 'warning'" 
                             class="w-6 h-6 bg-yellow-100 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
                            </svg>
                        </div>
                        
                        <!-- Info Icon -->
                        <div x-show="notification.type === 'info'" 
                             class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                    </div>
                    
                    <!-- Content -->
                    <div class="ml-3 w-0 flex-1">
                        <div x-show="notification.title" 
                             class="text-sm font-medium"
                             :class="{
                                'text-green-800': notification.type === 'success',
                                'text-red-800': notification.type === 'error',
                                'text-yellow-800': notification.type === 'warning',
                                'text-blue-800': notification.type === 'info'
                             }"
                             x-text="notification.title">
                        </div>
                        <div class="text-sm"
                             :class="{
                                'text-green-700': notification.type === 'success',
                                'text-red-700': notification.type === 'error',
                                'text-yellow-700': notification.type === 'warning',
                                'text-blue-700': notification.type === 'info'
                             }"
                             x-text="notification.message">
                        </div>
                        
                        <!-- Action Button (if provided) -->
                        <div x-show="notification.action" class="mt-3">
                            <button @click="notification.action.callback(); removeNotification(notification.id)"
                                    class="text-sm font-medium underline"
                                    :class="{
                                        'text-green-600 hover:text-green-500': notification.type === 'success',
                                        'text-red-600 hover:text-red-500': notification.type === 'error',
                                        'text-yellow-600 hover:text-yellow-500': notification.type === 'warning',
                                        'text-blue-600 hover:text-blue-500': notification.type === 'info'
                                    }"
                                    x-text="notification.action?.text">
                            </button>
                        </div>
                    </div>
                    
                    <!-- Close Button -->
                    <div class="ml-4 flex-shrink-0 flex">
                        <button @click="removeNotification(notification.id)"
                                class="rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2"
                                :class="{
                                    'focus:ring-green-500': notification.type === 'success',
                                    'focus:ring-red-500': notification.type === 'error',
                                    'focus:ring-yellow-500': notification.type === 'warning',
                                    'focus:ring-blue-500': notification.type === 'info'
                                }">
                            <span class="sr-only">Close</span>
                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                            </svg>
                        </button>
                    </div>
                </div>
                
                <!-- Progress Bar (for auto-dismiss) -->
                <div x-show="notification.autoDismiss && notification.duration > 0" 
                     class="mt-3 w-full bg-gray-200 rounded-full h-1">
                    <div class="h-1 rounded-full transition-all duration-100 ease-linear"
                         :class="{
                            'bg-green-500': notification.type === 'success',
                            'bg-red-500': notification.type === 'error',
                            'bg-yellow-500': notification.type === 'warning',
                            'bg-blue-500': notification.type === 'info'
                         }"
                         :style="`width: ${(notification.timeLeft / notification.duration) * 100}%`">
                    </div>
                </div>
            </div>
        </div>
    </template>
</div>

<!-- Laravel Session Flash Messages -->
@if(session('success') || session('error') || session('warning') || session('info'))
<script>
document.addEventListener('DOMContentLoaded', function() {
    @if(session('success'))
        window.dispatchEvent(new CustomEvent('show-notification', {
            detail: {
                type: 'success',
                message: '{{ session('success') }}',
                duration: 5000
            }
        }));
    @endif
    
    @if(session('error'))
        window.dispatchEvent(new CustomEvent('show-notification', {
            detail: {
                type: 'error',
                message: '{{ session('error') }}',
                duration: 7000
            }
        }));
    @endif
    
    @if(session('warning'))
        window.dispatchEvent(new CustomEvent('show-notification', {
            detail: {
                type: 'warning',
                message: '{{ session('warning') }}',
                duration: 6000
            }
        }));
    @endif
    
    @if(session('info'))
        window.dispatchEvent(new CustomEvent('show-notification', {
            detail: {
                type: 'info',
                message: '{{ session('info') }}',
                duration: 5000
            }
        }));
    @endif
});
</script>
@endif

<script>
function notificationSystem() {
    return {
        notifications: [],
        nextId: 1,
        
        showNotification(options) {
            const notification = {
                id: this.nextId++,
                type: options.type || 'info',
                title: options.title || '',
                message: options.message || '',
                action: options.action || null,
                autoDismiss: options.autoDismiss !== false,
                duration: options.duration || 5000,
                timeLeft: options.duration || 5000,
                show: false
            };
            
            this.notifications.push(notification);
            
            // Show with slight delay for animation
            setTimeout(() => {
                notification.show = true;
            }, 10);
            
            // Auto dismiss if enabled
            if (notification.autoDismiss) {
                this.startCountdown(notification);
            }
            
            // Limit to 5 notifications max
            if (this.notifications.length > 5) {
                this.removeNotification(this.notifications[0].id);
            }
        },
        
        removeNotification(id) {
            const notification = this.notifications.find(n => n.id === id);
            if (notification) {
                notification.show = false;
                // Remove from array after animation
                setTimeout(() => {
                    this.notifications = this.notifications.filter(n => n.id !== id);
                }, 300);
            }
        },
        
        startCountdown(notification) {
            const interval = setInterval(() => {
                notification.timeLeft -= 100;
                
                if (notification.timeLeft <= 0) {
                    clearInterval(interval);
                    this.removeNotification(notification.id);
                }
            }, 100);
            
            // Pause countdown on hover
            const notificationElement = document.querySelector(`[data-notification-id="${notification.id}"]`);
            if (notificationElement) {
                notificationElement.addEventListener('mouseenter', () => clearInterval(interval));
                notificationElement.addEventListener('mouseleave', () => {
                    if (notification.timeLeft > 0) {
                        this.startCountdown(notification);
                    }
                });
            }
        },
        
        clearAll() {
            this.notifications.forEach(notification => {
                notification.show = false;
            });
            
            setTimeout(() => {
                this.notifications = [];
            }, 300);
        }
    }
}

// Global notification functions
window.showSuccess = function(message, options = {}) {
    window.dispatchEvent(new CustomEvent('show-notification', {
        detail: { ...options, type: 'success', message }
    }));
};

window.showError = function(message, options = {}) {
    window.dispatchEvent(new CustomEvent('show-notification', {
        detail: { ...options, type: 'error', message }
    }));
};

window.showWarning = function(message, options = {}) {
    window.dispatchEvent(new CustomEvent('show-notification', {
        detail: { ...options, type: 'warning', message }
    }));
};

window.showInfo = function(message, options = {}) {
    window.dispatchEvent(new CustomEvent('show-notification', {
        detail: { ...options, type: 'info', message }
    }));
};

// Enhanced notification with action
window.showNotificationWithAction = function(message, type, actionText, actionCallback) {
    window.dispatchEvent(new CustomEvent('show-notification', {
        detail: {
            type,
            message,
            action: {
                text: actionText,
                callback: actionCallback
            },
            autoDismiss: false
        }
    }));
};
</script>
