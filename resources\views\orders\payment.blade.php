@extends('layouts.main')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-light via-white to-green-light/30">
    
    <!-- Header -->
    <section class="pt-8 pb-6">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-8">
                <h1 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4" data-aos="fade-up">
                    Pembayaran
                </h1>
                <p class="text-lg text-gray-600" data-aos="fade-up" data-aos-delay="100">
                    Selesaikan pembayaran untuk mengaktifkan tiket Anda
                </p>
            </div>

            <!-- Timer -->
            <div class="bg-orange-50 border border-orange-200 rounded-xl p-4 mb-8" data-aos="fade-up" data-aos-delay="200">
                <div class="flex items-center justify-center space-x-2">
                    <svg class="w-5 h-5 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                    <span class="text-orange-700 font-semibold">
                        Selesaikan pembayaran dalam: 
                        <span id="countdown" class="font-bold text-orange-800"></span>
                    </span>
                </div>
            </div>
        </div>
    </section>

    <!-- Payment Content -->
    <section class="pb-16">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                
                <!-- Payment Methods -->
                <div class="lg:col-span-2 space-y-6">
                    
                    <!-- Order Summary -->
                    <div class="bg-white rounded-2xl shadow-lg p-6" data-aos="fade-up">
                        <h2 class="text-xl font-bold text-gray-900 mb-4">Detail Pesanan</h2>
                        <div class="flex items-start space-x-4">
                            <img src="{{ $order->event->poster_url }}" 
                                 alt="{{ $order->event->title }}" 
                                 class="w-20 h-20 object-cover rounded-lg">
                            <div class="flex-1">
                                <h3 class="font-semibold text-gray-900 mb-2">{{ $order->event->title }}</h3>
                                <div class="space-y-1 text-sm text-gray-600">
                                    <div>{{ $order->event->start_date->format('d M Y, H:i') }} WIB</div>
                                    <div>{{ $order->event->venue_name }}, {{ $order->event->city }}</div>
                                    <div>{{ $order->quantity }} tiket × {{ $order->customer_name }}</div>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-lg font-bold text-primary">
                                    Rp {{ number_format($order->total_amount, 0, ',', '.') }}
                                </div>
                                <div class="text-sm text-gray-500">
                                    Order #{{ $order->order_number }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Method Selection -->
                    <div class="bg-white rounded-2xl shadow-lg p-6" data-aos="fade-up" data-aos-delay="100">
                        <h2 class="text-xl font-bold text-gray-900 mb-6">Pilih Metode Pembayaran</h2>
                        
                        <form method="POST" action="{{ route('orders.process-payment', $order) }}" x-data="paymentForm()">
                            @csrf
                            
                            <div class="space-y-4">
                                @foreach($paymentMethods as $key => $method)
                                    <label class="block">
                                        <input type="radio" 
                                               name="payment_method" 
                                               value="{{ $key }}"
                                               x-model="selectedMethod"
                                               class="sr-only peer">
                                        <div class="p-4 border-2 border-gray-200 rounded-xl cursor-pointer peer-checked:border-primary peer-checked:bg-primary/5 transition-all duration-200">
                                            <div class="flex items-center justify-between">
                                                <div class="flex items-center space-x-4">
                                                    <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                                                        @if($key === 'bank_transfer')
                                                            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"/>
                                                            </svg>
                                                        @elseif($key === 'e_wallet')
                                                            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"/>
                                                            </svg>
                                                        @elseif($key === 'credit_card')
                                                            <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"/>
                                                            </svg>
                                                        @else
                                                            <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"/>
                                                            </svg>
                                                        @endif
                                                    </div>
                                                    <div>
                                                        <h3 class="font-semibold text-gray-900">{{ $method['name'] }}</h3>
                                                        <p class="text-sm text-gray-600">{{ $method['description'] }}</p>
                                                        <p class="text-xs text-gray-500 mt-1">
                                                            Proses: {{ $method['processing_time'] }}
                                                            @if($method['fee'] > 0)
                                                                • Biaya: {{ is_numeric($method['fee']) ? $method['fee'] . '%' : $method['fee'] }}
                                                            @endif
                                                        </p>
                                                    </div>
                                                </div>
                                                <div class="w-5 h-5 border-2 border-gray-300 rounded-full peer-checked:border-primary peer-checked:bg-primary flex items-center justify-center">
                                                    <div class="w-2 h-2 bg-white rounded-full opacity-0 peer-checked:opacity-100"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </label>
                                @endforeach
                            </div>

                            <!-- Payment Details -->
                            <div x-show="selectedMethod" class="mt-6">
                                
                                <!-- Bank Transfer Details -->
                                <div x-show="selectedMethod === 'bank_transfer'" class="bg-blue-50 border border-blue-200 rounded-xl p-6">
                                    <h4 class="font-semibold text-blue-900 mb-4">Informasi Transfer Bank</h4>
                                    <div class="space-y-4">
                                        @foreach($paymentMethods['bank_transfer']['banks'] as $bank)
                                            <div class="bg-white rounded-lg p-4">
                                                <div class="flex justify-between items-center">
                                                    <div>
                                                        <div class="font-semibold text-gray-900">{{ $bank['name'] }}</div>
                                                        <div class="text-sm text-gray-600">{{ $bank['holder'] }}</div>
                                                    </div>
                                                    <div class="text-right">
                                                        <div class="font-mono text-lg font-bold text-gray-900">{{ $bank['account'] }}</div>
                                                        <button type="button" onclick="copyToClipboard('{{ $bank['account'] }}')" 
                                                                class="text-xs text-primary hover:text-accent">
                                                            Salin Nomor
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                    <input type="hidden" name="payment_details[bank]" value="">
                                </div>

                                <!-- E-Wallet Details -->
                                <div x-show="selectedMethod === 'e_wallet'" class="bg-green-50 border border-green-200 rounded-xl p-6">
                                    <h4 class="font-semibold text-green-900 mb-4">Pilih E-Wallet</h4>
                                    <div class="grid grid-cols-2 md:grid-cols-3 gap-3">
                                        @foreach($paymentMethods['e_wallet']['providers'] as $provider)
                                            <label class="block">
                                                <input type="radio" 
                                                       name="payment_details[provider]" 
                                                       value="{{ $provider }}"
                                                       class="sr-only peer">
                                                <div class="p-3 border-2 border-gray-200 rounded-lg cursor-pointer peer-checked:border-green-500 peer-checked:bg-green-50 text-center transition-all duration-200">
                                                    <div class="font-semibold text-gray-900">{{ $provider }}</div>
                                                </div>
                                            </label>
                                        @endforeach
                                    </div>
                                </div>

                                <!-- Credit Card Details -->
                                <div x-show="selectedMethod === 'credit_card'" class="bg-purple-50 border border-purple-200 rounded-xl p-6">
                                    <h4 class="font-semibold text-purple-900 mb-4">Informasi Kartu Kredit</h4>
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div class="md:col-span-2">
                                            <label class="block text-sm font-medium text-gray-700 mb-2">Nomor Kartu</label>
                                            <input type="text" 
                                                   name="payment_details[card_number]" 
                                                   placeholder="1234 5678 9012 3456"
                                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:border-primary focus:outline-none">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">Tanggal Kadaluarsa</label>
                                            <input type="text" 
                                                   name="payment_details[expiry]" 
                                                   placeholder="MM/YY"
                                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:border-primary focus:outline-none">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">CVV</label>
                                            <input type="text" 
                                                   name="payment_details[cvv]" 
                                                   placeholder="123"
                                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:border-primary focus:outline-none">
                                        </div>
                                    </div>
                                </div>

                                <!-- Cash Payment Details -->
                                <div x-show="selectedMethod === 'cash'" class="bg-yellow-50 border border-yellow-200 rounded-xl p-6">
                                    <h4 class="font-semibold text-yellow-900 mb-4">Pembayaran di Tempat</h4>
                                    <div class="flex items-start space-x-3">
                                        <svg class="w-5 h-5 text-yellow-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                        </svg>
                                        <div class="text-sm text-yellow-800">
                                            <p class="font-semibold mb-2">Ketentuan Pembayaran di Tempat:</p>
                                            <ul class="list-disc list-inside space-y-1">
                                                <li>Bayar saat check-in di lokasi event</li>
                                                <li>Bawa tiket digital dan identitas diri</li>
                                                <li>Pembayaran hanya menerima uang tunai</li>
                                                <li>Datang 30 menit sebelum event dimulai</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Submit Button -->
                            <div class="mt-8">
                                <button type="submit" 
                                        :disabled="!selectedMethod || processing"
                                        @click="processing = true"
                                        class="w-full bg-gradient-to-r from-primary to-secondary text-white py-4 rounded-xl font-bold text-lg hover:shadow-lg transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed">
                                    <span x-show="!processing">Bayar Sekarang</span>
                                    <span x-show="processing" class="flex items-center justify-center">
                                        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                        Memproses Pembayaran...
                                    </span>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Order Summary Sidebar -->
                <div class="lg:col-span-1">
                    <div class="sticky top-24">
                        <div class="bg-white rounded-2xl shadow-lg p-6" data-aos="fade-left">
                            <h3 class="text-lg font-bold text-gray-900 mb-4">Ringkasan Pembayaran</h3>
                            
                            <div class="space-y-3 mb-6">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Subtotal</span>
                                    <span>Rp {{ number_format($order->subtotal, 0, ',', '.') }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Biaya admin</span>
                                    <span>Rp {{ number_format($order->admin_fee, 0, ',', '.') }}</span>
                                </div>
                                @if($order->discount_amount > 0)
                                    <div class="flex justify-between text-green-600">
                                        <span>Diskon</span>
                                        <span>-Rp {{ number_format($order->discount_amount, 0, ',', '.') }}</span>
                                    </div>
                                @endif
                                <div class="border-t border-gray-200 pt-3">
                                    <div class="flex justify-between items-center">
                                        <span class="text-lg font-bold text-gray-900">Total</span>
                                        <span class="text-lg font-bold text-primary">
                                            Rp {{ number_format($order->total_amount, 0, ',', '.') }}
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <!-- Security Info -->
                            <div class="bg-gray-50 rounded-lg p-4">
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg class="w-4 h-4 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
                                    </svg>
                                    Pembayaran aman dengan enkripsi SSL
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
@endsection

@push('scripts')
<script>
function paymentForm() {
    return {
        selectedMethod: '',
        processing: false
    }
}

// Countdown timer
function startCountdown() {
    const expiresAt = new Date('{{ $order->expires_at }}').getTime();
    const countdownElement = document.getElementById('countdown');
    
    const timer = setInterval(function() {
        const now = new Date().getTime();
        const distance = expiresAt - now;
        
        if (distance < 0) {
            clearInterval(timer);
            countdownElement.innerHTML = "EXPIRED";
            window.location.reload();
            return;
        }
        
        const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((distance % (1000 * 60)) / 1000);
        
        countdownElement.innerHTML = minutes + "m " + seconds + "s";
    }, 1000);
}

function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        window.showNotification('Nomor rekening berhasil disalin!', 'success');
    });
}

document.addEventListener('DOMContentLoaded', function() {
    startCountdown();
});
</script>
@endpush
