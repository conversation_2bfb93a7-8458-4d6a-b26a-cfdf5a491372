<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Category;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Musik & Konser',
                'slug' => 'musik-konser',
                'description' => 'Konser musik, festival musik, dan pertunjukan musik lainnya',
                'icon' => 'musical-note',
                'color' => '#A8D5BA',
                'sort_order' => 1,
            ],
            [
                'name' => 'Workshop & Seminar',
                'slug' => 'workshop-seminar',
                'description' => 'Workshop, seminar, pelatihan, dan acara edukasi',
                'icon' => 'academic-cap',
                'color' => '#F4A261',
                'sort_order' => 2,
            ],
            [
                'name' => 'Festival & Pameran',
                'slug' => 'festival-pameran',
                'description' => 'Festival budaya, pameran seni, dan acara komunitas',
                'icon' => 'flag',
                'color' => '#E76F51',
                'sort_order' => 3,
            ],
            [
                'name' => 'Olahraga',
                'slug' => 'olahraga',
                'description' => 'Pertandingan olahraga, turnamen, dan acara kebugaran',
                'icon' => 'trophy',
                'color' => '#2A9D8F',
                'sort_order' => 4,
            ],
            [
                'name' => 'Teknologi',
                'slug' => 'teknologi',
                'description' => 'Conference teknologi, hackathon, dan acara IT',
                'icon' => 'computer-desktop',
                'color' => '#264653',
                'sort_order' => 5,
            ],
            [
                'name' => 'Kuliner',
                'slug' => 'kuliner',
                'description' => 'Food festival, cooking class, dan acara kuliner',
                'icon' => 'cake',
                'color' => '#E9C46A',
                'sort_order' => 6,
            ],
            [
                'name' => 'Seni & Budaya',
                'slug' => 'seni-budaya',
                'description' => 'Pertunjukan seni, teater, dan acara budaya',
                'icon' => 'paint-brush',
                'color' => '#F76C6C',
                'sort_order' => 7,
            ],
            [
                'name' => 'Bisnis & Networking',
                'slug' => 'bisnis-networking',
                'description' => 'Business conference, networking event, dan startup meetup',
                'icon' => 'briefcase',
                'color' => '#A8DADC',
                'sort_order' => 8,
            ],
        ];

        foreach ($categories as $category) {
            Category::create($category);
        }
    }
}
