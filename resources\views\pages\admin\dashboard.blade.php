@extends('layouts.app')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Dashboard Header -->
    <div class="mb-8" data-aos="fade-up">
        <h1 class="text-2xl font-bold mb-2">Dashboard Admin</h1>
        <p class="text-gray-600">Kelola event, pengguna, dan statistik penjualan</p>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Total Events -->
        <div class="bg-white rounded-xl shadow-sm p-6" data-aos="fade-up" data-aos-delay="100">
            <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-primary/10 rounded-lg">
                    <svg class="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                              d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                    </svg>
                </div>
                <div class="text-right">
                    <p class="text-sm text-gray-600">Total Event</p>
                    <p class="text-2xl font-bold">{{ $totalEvents }}</p>
                </div>
            </div>
            <div class="flex items-center text-sm">
                <span class="text-green-500 flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"/>
                    </svg>
                    {{ $eventGrowth }}%
                </span>
                <span class="text-gray-600 ml-2">dari bulan lalu</span>
            </div>
        </div>

        <!-- Total Users -->
        <div class="bg-white rounded-xl shadow-sm p-6" data-aos="fade-up" data-aos-delay="200">
            <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-blue-50 rounded-lg">
                    <svg class="w-6 h-6 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                              d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                    </svg>
                </div>
                <div class="text-right">
                    <p class="text-sm text-gray-600">Total Pengguna</p>
                    <p class="text-2xl font-bold">{{ $totalUsers }}</p>
                </div>
            </div>
            <div class="flex items-center text-sm">
                <span class="text-green-500 flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"/>
                    </svg>
                    {{ $userGrowth }}%
                </span>
                <span class="text-gray-600 ml-2">dari bulan lalu</span>
            </div>
        </div>

        <!-- Total Sales -->
        <div class="bg-white rounded-xl shadow-sm p-6" data-aos="fade-up" data-aos-delay="300">
            <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-green-50 rounded-lg">
                    <svg class="w-6 h-6 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                              d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                </div>
                <div class="text-right">
                    <p class="text-sm text-gray-600">Total Penjualan</p>
                    <p class="text-2xl font-bold">Rp {{ number_format($totalSales, 0, ',', '.') }}</p>
                </div>
            </div>
            <div class="flex items-center text-sm">
                <span class="text-green-500 flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"/>
                    </svg>
                    {{ $salesGrowth }}%
                </span>
                <span class="text-gray-600 ml-2">dari bulan lalu</span>
            </div>
        </div>

        <!-- Active Tickets -->
        <div class="bg-white rounded-xl shadow-sm p-6" data-aos="fade-up" data-aos-delay="400">
            <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-purple-50 rounded-lg">
                    <svg class="w-6 h-6 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                              d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"/>
                    </svg>
                </div>
                <div class="text-right">
                    <p class="text-sm text-gray-600">Tiket Aktif</p>
                    <p class="text-2xl font-bold">{{ $activeTickets }}</p>
                </div>
            </div>
            <div class="flex items-center text-sm">
                <span class="text-gray-600">{{ $ticketUsageRate }}% tingkat penggunaan</span>
            </div>
        </div>
    </div>

    <!-- Recent Events & Users -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Recent Events -->
        <div class="bg-white rounded-xl shadow-sm overflow-hidden" data-aos="fade-up" data-aos-delay="500">
            <div class="p-6">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-lg font-semibold">Event Terbaru</h2>
                    <a href="{{ route('admin.events.index') }}" class="text-primary hover:text-primary/80 transition-colors">
                        Lihat Semua
                    </a>
                </div>
                <div class="space-y-4">
                    @foreach($recentEvents as $event)
                    <div class="flex items-center space-x-4">
                        <img src="{{ $event->poster_url }}" 
                             alt="{{ $event->title }}" 
                             class="w-16 h-16 rounded-lg object-cover">
                        <div class="flex-1 min-w-0">
                            <h3 class="font-medium truncate">{{ $event->title }}</h3>
                            <p class="text-sm text-gray-600">{{ $event->start_date->format('d M Y') }}</p>
                        </div>
                        <div class="text-right">
                            <p class="font-medium">{{ $event->tickets_count }} tiket</p>
                            <p class="text-sm text-gray-600">Rp {{ number_format($event->price, 0, ',', '.') }}</p>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>

        <!-- Recent Users -->
        <div class="bg-white rounded-xl shadow-sm overflow-hidden" data-aos="fade-up" data-aos-delay="600">
            <div class="p-6">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-lg font-semibold">Pengguna Terbaru</h2>
                    <a href="{{ route('admin.users.index') }}" class="text-primary hover:text-primary/80 transition-colors">
                        Lihat Semua
                    </a>
                </div>
                <div class="space-y-4">
                    @foreach($recentUsers as $user)
                    <div class="flex items-center space-x-4">
                        <img src="{{ $user->profile_photo_url }}" 
                             alt="{{ $user->name }}" 
                             class="w-10 h-10 rounded-full object-cover">
                        <div class="flex-1 min-w-0">
                            <h3 class="font-medium truncate">{{ $user->name }}</h3>
                            <p class="text-sm text-gray-600 truncate">{{ $user->email }}</p>
                        </div>
                        <span class="px-2 py-1 text-xs rounded-full 
                                   {{ $user->role === 'admin' ? 'bg-red-100 text-red-700' : 
                                      ($user->role === 'staff' ? 'bg-blue-100 text-blue-700' : 
                                      ($user->role === 'penjual' ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-700')) }}">
                            {{ ucfirst($user->role) }}
                        </span>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
</div>
@endsection