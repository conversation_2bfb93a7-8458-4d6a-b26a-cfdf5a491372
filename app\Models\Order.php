<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class Order extends Model
{
    use HasFactory;

    protected $fillable = [
        'order_number',
        'user_id',
        'event_id',
        'quantity',
        'unit_price',
        'subtotal',
        'admin_fee',
        'discount_amount',
        'total_amount',
        'payment_status',
        'payment_method',
        'payment_reference',
        'paid_at',
        'expires_at',
        'customer_name',
        'customer_email',
        'customer_phone',
        'payment_data',
        'notes',
        'discount_code',
        'status',
        'confirmed_at',
        'cancelled_at',
        'cancellation_reason',
    ];

    protected $casts = [
        'quantity' => 'integer',
        'unit_price' => 'decimal:2',
        'subtotal' => 'decimal:2',
        'admin_fee' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'paid_at' => 'datetime',
        'expires_at' => 'datetime',
        'confirmed_at' => 'datetime',
        'cancelled_at' => 'datetime',
        'payment_data' => 'array',
    ];

    /**
     * Payment status constants
     */
    public const PAYMENT_PENDING = 'pending';
    public const PAYMENT_PAID = 'paid';
    public const PAYMENT_FAILED = 'failed';
    public const PAYMENT_CANCELLED = 'cancelled';
    public const PAYMENT_REFUNDED = 'refunded';

    /**
     * Order status constants
     */
    public const STATUS_PENDING = 'pending';
    public const STATUS_CONFIRMED = 'confirmed';
    public const STATUS_CANCELLED = 'cancelled';
    public const STATUS_COMPLETED = 'completed';

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($order) {
            if (empty($order->order_number)) {
                $order->order_number = self::generateOrderNumber();
            }
            
            // Set payment expiry (24 hours from creation)
            if (!$order->expires_at) {
                $order->expires_at = now()->addHours(24);
            }
        });
    }

    /**
     * Generate unique order number
     */
    public static function generateOrderNumber(): string
    {
        do {
            $number = 'ORD-' . date('Ymd') . '-' . strtoupper(Str::random(5));
        } while (self::where('order_number', $number)->exists());

        return $number;
    }

    /**
     * User relationship
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Event relationship
     */
    public function event(): BelongsTo
    {
        return $this->belongsTo(Event::class);
    }

    /**
     * Tickets relationship
     */
    public function tickets(): HasMany
    {
        return $this->hasMany(Ticket::class);
    }

    /**
     * Check if order is paid
     */
    public function isPaid(): bool
    {
        return $this->payment_status === self::PAYMENT_PAID;
    }

    /**
     * Check if order is expired
     */
    public function isExpired(): bool
    {
        return $this->expires_at && now()->isAfter($this->expires_at) && !$this->isPaid();
    }

    /**
     * Check if order can be cancelled
     */
    public function canBeCancelled(): bool
    {
        return in_array($this->status, [self::STATUS_PENDING, self::STATUS_CONFIRMED]) 
               && !$this->event->hasStarted();
    }

    /**
     * Mark order as paid
     */
    public function markAsPaid(array $paymentData = []): void
    {
        $this->update([
            'payment_status' => self::PAYMENT_PAID,
            'status' => self::STATUS_CONFIRMED,
            'paid_at' => now(),
            'confirmed_at' => now(),
            'payment_data' => array_merge($this->payment_data ?? [], $paymentData),
        ]);

        // Generate tickets
        $this->generateTickets();
    }

    /**
     * Cancel order
     */
    public function cancel(string $reason = null): void
    {
        $this->update([
            'status' => self::STATUS_CANCELLED,
            'payment_status' => self::PAYMENT_CANCELLED,
            'cancelled_at' => now(),
            'cancellation_reason' => $reason,
        ]);

        // Restore event capacity
        $this->event->increment('available_capacity', $this->quantity);
    }

    /**
     * Generate tickets for this order
     */
    public function generateTickets(): void
    {
        if ($this->tickets()->count() > 0) {
            return; // Tickets already generated
        }

        for ($i = 0; $i < $this->quantity; $i++) {
            Ticket::create([
                'ticket_number' => Ticket::generateTicketNumber(),
                'qr_code' => Ticket::generateQrCode(),
                'event_id' => $this->event_id,
                'buyer_id' => $this->user_id,
                'order_id' => $this->id,
                'attendee_name' => $this->customer_name,
                'attendee_email' => $this->customer_email,
                'attendee_phone' => $this->customer_phone,
                'price' => $this->unit_price,
                'admin_fee' => $this->admin_fee / $this->quantity,
                'total_paid' => $this->total_amount / $this->quantity,
            ]);
        }
    }

    /**
     * Get formatted total amount
     */
    public function getFormattedTotalAttribute(): string
    {
        return 'Rp ' . number_format($this->total_amount, 0, ',', '.');
    }

    /**
     * Scope for paid orders
     */
    public function scopePaid($query)
    {
        return $query->where('payment_status', self::PAYMENT_PAID);
    }

    /**
     * Scope for pending orders
     */
    public function scopePending($query)
    {
        return $query->where('payment_status', self::PAYMENT_PENDING);
    }

    /**
     * Scope for expired orders
     */
    public function scopeExpired($query)
    {
        return $query->where('expires_at', '<', now())
                    ->where('payment_status', '!=', self::PAYMENT_PAID);
    }

    /**
     * Scope for specific user
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }
}
