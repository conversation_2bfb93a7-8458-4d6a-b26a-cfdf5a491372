<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tickets', function (Blueprint $table) {
            $table->id();
            $table->string('ticket_number')->unique(); // TIK-YYYYMMDD-XXXXX
            $table->string('qr_code')->unique(); // QR Code string
            $table->foreignId('event_id')->constrained()->onDelete('cascade');
            $table->foreignId('buyer_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('order_id')->constrained()->onDelete('cascade');
            
            // Ticket Details
            $table->string('attendee_name');
            $table->string('attendee_email');
            $table->string('attendee_phone')->nullable();
            
            // Pricing
            $table->decimal('price', 12, 2);
            $table->decimal('admin_fee', 12, 2)->default(0);
            $table->decimal('total_paid', 12, 2);
            
            // Status & Validation
            $table->enum('status', ['active', 'used', 'cancelled', 'refunded'])->default('active');
            $table->datetime('used_at')->nullable();
            $table->foreignId('validated_by')->nullable()->constrained('users');
            $table->text('validation_notes')->nullable();
            
            // Download & Access
            $table->string('download_token')->nullable();
            $table->integer('download_count')->default(0);
            $table->datetime('last_downloaded_at')->nullable();
            
            $table->timestamps();
            
            // Indexes
            $table->index(['event_id', 'status']);
            $table->index(['buyer_id', 'status']);
            $table->index(['order_id']);
            $table->index('qr_code');
            $table->index('ticket_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tickets');
    }
};
