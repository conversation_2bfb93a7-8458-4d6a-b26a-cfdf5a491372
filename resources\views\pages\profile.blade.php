@extends('layouts.app')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="max-w-3xl mx-auto">
        <!-- Profile Header -->
        <div class="bg-white rounded-xl shadow-sm overflow-hidden mb-6" data-aos="fade-up">
            <div class="bg-primary h-32"></div>
            <div class="px-6 pb-6">
                <div class="flex flex-col items-center -mt-16">
                    <div class="relative">
                        <img src="{{ auth()->user()->profile_photo_url }}" 
                             alt="{{ auth()->user()->name }}" 
                             class="w-32 h-32 rounded-full border-4 border-white bg-white object-cover">
                        <button onclick="document.getElementById('profile_photo').click()" 
                                class="absolute bottom-0 right-0 bg-white rounded-full p-2 shadow-md hover:bg-gray-50">
                            <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                      d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"/>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                      d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"/>
                            </svg>
                        </button>
                        <input type="file" 
                               id="profile_photo" 
                               wire:model="photo" 
                               class="hidden" 
                               accept="image/*">
                    </div>
                    <h1 class="text-2xl font-bold mt-4">{{ auth()->user()->name }}</h1>
                    <p class="text-gray-600">{{ auth()->user()->email }}</p>
                    <span class="mt-2 px-3 py-1 bg-primary/10 text-primary rounded-full text-sm">
                        {{ ucfirst(auth()->user()->role) }}
                    </span>
                </div>
            </div>
        </div>

        <!-- Profile Form -->
        <div class="bg-white rounded-xl shadow-sm overflow-hidden" data-aos="fade-up" data-aos-delay="100">
            <div class="p-6">
                <h2 class="text-lg font-semibold mb-6">Informasi Profil</h2>
                <form wire:submit.prevent="updateProfile" class="space-y-6">
                    <!-- Name -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Nama Lengkap</label>
                        <input type="text" 
                               id="name" 
                               wire:model.defer="name" 
                               class="w-full border-gray-300 rounded-lg focus:ring-primary focus:border-primary"
                               required>
                        @error('name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Email -->
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                        <input type="email" 
                               id="email" 
                               wire:model.defer="email" 
                               class="w-full border-gray-300 rounded-lg focus:ring-primary focus:border-primary"
                               required>
                        @error('email')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Phone -->
                    <div>
                        <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">Nomor Telepon</label>
                        <input type="tel" 
                               id="phone" 
                               wire:model.defer="phone" 
                               class="w-full border-gray-300 rounded-lg focus:ring-primary focus:border-primary">
                        @error('phone')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Password -->
                    <div>
                        <label for="current_password" class="block text-sm font-medium text-gray-700 mb-2">Password Saat Ini</label>
                        <input type="password" 
                               id="current_password" 
                               wire:model.defer="current_password" 
                               class="w-full border-gray-300 rounded-lg focus:ring-primary focus:border-primary">
                        @error('current_password')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="password" class="block text-sm font-medium text-gray-700 mb-2">Password Baru</label>
                            <input type="password" 
                                   id="password" 
                                   wire:model.defer="password" 
                                   class="w-full border-gray-300 rounded-lg focus:ring-primary focus:border-primary">
                            @error('password')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="password_confirmation" class="block text-sm font-medium text-gray-700 mb-2">Konfirmasi Password</label>
                            <input type="password" 
                                   id="password_confirmation" 
                                   wire:model.defer="password_confirmation" 
                                   class="w-full border-gray-300 rounded-lg focus:ring-primary focus:border-primary">
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="flex justify-end">
                        <button type="submit" 
                                class="bg-primary text-white px-6 py-2 rounded-lg font-medium hover:bg-primary/90 transition-colors"
                                wire:loading.attr="disabled"
                                wire:loading.class="opacity-75">
                            <span wire:loading.remove>Simpan Perubahan</span>
                            <span wire:loading>Menyimpan...</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Notification Settings -->
        <div class="bg-white rounded-xl shadow-sm overflow-hidden mt-6" data-aos="fade-up" data-aos-delay="200">
            <div class="p-6">
                <h2 class="text-lg font-semibold mb-6">Pengaturan Notifikasi</h2>
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="font-medium">Email Notifikasi</h3>
                            <p class="text-sm text-gray-600">Terima notifikasi event melalui email</p>
                        </div>
                        <button wire:click="toggleEmailNotification" 
                                class="relative inline-flex items-center h-6 rounded-full w-11 
                                       {{ $emailNotification ? 'bg-primary' : 'bg-gray-200' }}">
                            <span class="sr-only">Toggle email notification</span>
                            <span class="inline-block w-4 h-4 transform transition-transform bg-white rounded-full 
                                       {{ $emailNotification ? 'translate-x-6' : 'translate-x-1' }}"></span>
                        </button>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="font-medium">Push Notifikasi</h3>
                            <p class="text-sm text-gray-600">Terima notifikasi langsung di browser</p>
                        </div>
                        <button wire:click="togglePushNotification" 
                                class="relative inline-flex items-center h-6 rounded-full w-11 
                                       {{ $pushNotification ? 'bg-primary' : 'bg-gray-200' }}">
                            <span class="sr-only">Toggle push notification</span>
                            <span class="inline-block w-4 h-4 transform transition-transform bg-white rounded-full 
                                       {{ $pushNotification ? 'translate-x-6' : 'translate-x-1' }}"></span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection