@extends('layouts.auth')

@section('content')
<div x-data="{ countdown: 60, canResend: false }" 
     x-init="
        let timer = setInterval(() => {
            countdown--;
            if (countdown <= 0) {
                canResend = true;
                clearInterval(timer);
            }
        }, 1000);
     ">
    <!-- Header -->
    <div class="text-center mb-8">
        <div class="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg class="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
            </svg>
        </div>
        <h2 class="text-2xl font-bold text-gray-900 mb-2"><PERSON><PERSON><PERSON><PERSON><PERSON></h2>
        <p class="text-gray-600">
            <PERSON><PERSON> telah mengirim kode OTP 6 digit ke email Anda
        </p>
        <p class="text-sm text-primary font-medium mt-2">
            {{ auth()->user()->email }}
        </p>
    </div>

    <!-- OTP Form -->
    <form method="POST" action="{{ route('auth.verify-email') }}" class="space-y-6">
        @csrf

        <!-- OTP Input -->
        <div>
            <label for="otp" class="block text-sm font-medium text-gray-700 mb-2 text-center">
                Masukkan Kode OTP
            </label>
            <div class="flex justify-center">
                <input type="text" 
                       id="otp" 
                       name="otp" 
                       maxlength="6"
                       class="input-field w-32 px-4 py-3 text-center text-2xl font-bold tracking-widest rounded-xl focus:outline-none @error('otp') border-red-500 @enderror"
                       placeholder="000000"
                       pattern="[0-9]{6}"
                       inputmode="numeric"
                       autocomplete="one-time-code"
                       required>
            </div>
            @error('otp')
            <p class="mt-2 text-sm text-red-600 text-center">{{ $message }}</p>
            @enderror
            <p class="mt-2 text-xs text-gray-500 text-center">
                Kode OTP berlaku selama 10 menit
            </p>
        </div>

        <!-- Submit Button -->
        <button type="submit" 
                class="btn-primary w-full py-3 px-4 rounded-xl font-semibold text-lg">
            Verifikasi Email
        </button>
    </form>

    <!-- Resend OTP -->
    <div class="mt-6 text-center">
        <p class="text-gray-600 mb-3">Tidak menerima kode?</p>
        
        <div x-show="!canResend" class="text-sm text-gray-500">
            Kirim ulang dalam <span x-text="countdown" class="font-medium text-primary"></span> detik
        </div>

        <form x-show="canResend" 
              method="POST" 
              action="{{ route('auth.resend-otp') }}" 
              class="inline">
            @csrf
            <button type="submit" 
                    class="text-primary hover:text-accent font-medium transition-colors duration-200">
                Kirim Ulang Kode OTP
            </button>
        </form>
    </div>

    <!-- Help Text -->
    <div class="mt-8 p-4 bg-blue-50 rounded-xl">
        <div class="flex items-start space-x-3">
            <svg class="w-5 h-5 text-blue-500 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
            <div class="text-sm text-blue-700">
                <p class="font-medium mb-1">Tips:</p>
                <ul class="space-y-1 text-xs">
                    <li>• Periksa folder spam/junk email Anda</li>
                    <li>• Pastikan email yang dimasukkan benar</li>
                    <li>• Kode OTP berlaku selama 10 menit</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Change Email -->
    <div class="text-center mt-6">
        <a href="{{ route('logout') }}" 
           class="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 transition-colors duration-200">
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
            </svg>
            Ganti email atau logout
        </a>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const otpInput = document.getElementById('otp');
    
    // Auto-submit when 6 digits entered
    otpInput.addEventListener('input', function(e) {
        const value = e.target.value.replace(/\D/g, ''); // Remove non-digits
        e.target.value = value;
        
        if (value.length === 6) {
            // Auto-submit form after short delay
            setTimeout(() => {
                e.target.closest('form').submit();
            }, 500);
        }
    });
    
    // Prevent non-numeric input
    otpInput.addEventListener('keypress', function(e) {
        if (!/[0-9]/.test(e.key) && !['Backspace', 'Delete', 'Tab', 'Enter'].includes(e.key)) {
            e.preventDefault();
        }
    });
});
</script>
@endsection
