@extends('layouts.main')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-light via-white to-green-light/30">
    
    <!-- Header -->
    <section class="pt-8 pb-6">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-8">
                <h1 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4" data-aos="fade-up">
                    QR Code Scanner
                </h1>
                <p class="text-lg text-gray-600" data-aos="fade-up" data-aos-delay="100">
                    Scan QR code tiket untuk validasi masuk event
                </p>
            </div>
        </div>
    </section>

    <!-- Scanner Interface -->
    <section class="pb-16">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                
                <!-- Scanner -->
                <div class="bg-white rounded-2xl shadow-lg p-6" data-aos="fade-up">
                    <h2 class="text-xl font-bold text-gray-900 mb-6">Scanner QR Code</h2>
                    
                    <!-- Camera View -->
                    <div class="relative mb-6">
                        <div id="scanner-container" class="relative">
                            <video id="scanner-video" 
                                   class="w-full h-80 object-cover rounded-lg bg-gray-100"
                                   autoplay 
                                   muted 
                                   playsinline>
                            </video>
                            
                            <!-- Scanner Overlay -->
                            <div class="absolute inset-0 flex items-center justify-center pointer-events-none">
                                <div class="w-48 h-48 border-4 border-primary rounded-lg relative">
                                    <!-- Corner indicators -->
                                    <div class="absolute -top-1 -left-1 w-6 h-6 border-t-4 border-l-4 border-primary"></div>
                                    <div class="absolute -top-1 -right-1 w-6 h-6 border-t-4 border-r-4 border-primary"></div>
                                    <div class="absolute -bottom-1 -left-1 w-6 h-6 border-b-4 border-l-4 border-primary"></div>
                                    <div class="absolute -bottom-1 -right-1 w-6 h-6 border-b-4 border-r-4 border-primary"></div>
                                    
                                    <!-- Scanning line -->
                                    <div class="absolute top-0 left-0 w-full h-1 bg-primary animate-pulse"></div>
                                </div>
                            </div>
                            
                            <!-- Status Overlay -->
                            <div id="scanner-status" class="absolute top-4 left-4 right-4">
                                <div class="bg-black/70 text-white px-4 py-2 rounded-lg text-center">
                                    <span id="status-text">Menginisialisasi kamera...</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Camera not available fallback -->
                        <div id="no-camera" class="hidden">
                            <div class="w-full h-80 bg-gray-100 rounded-lg flex items-center justify-center">
                                <div class="text-center">
                                    <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"/>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"/>
                                    </svg>
                                    <p class="text-gray-600">Kamera tidak tersedia</p>
                                    <p class="text-sm text-gray-500 mt-2">Gunakan input manual di bawah</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Manual Input -->
                    <div class="border-t border-gray-200 pt-6">
                        <h3 class="font-semibold text-gray-900 mb-4">Input Manual</h3>
                        <form id="manual-form" class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    QR Code atau Nomor Tiket
                                </label>
                                <input type="text" 
                                       id="manual-input" 
                                       placeholder="Masukkan QR code atau nomor tiket..."
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:border-primary focus:outline-none">
                            </div>
                            <button type="submit" 
                                    class="w-full bg-primary text-white py-3 rounded-lg hover:bg-primary/90 transition-colors duration-200">
                                Validasi Tiket
                            </button>
                        </form>
                    </div>
                    
                    <!-- Scanner Controls -->
                    <div class="border-t border-gray-200 pt-6 mt-6">
                        <div class="flex space-x-4">
                            <button id="start-scanner" 
                                    class="flex-1 bg-green-600 text-white py-3 rounded-lg hover:bg-green-700 transition-colors duration-200">
                                <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1.01M15 10h1.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                Mulai Scanner
                            </button>
                            <button id="stop-scanner" 
                                    class="flex-1 bg-red-600 text-white py-3 rounded-lg hover:bg-red-700 transition-colors duration-200">
                                <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 9l6 6m0-6l-6 6"/>
                                </svg>
                                Stop Scanner
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Validation Results -->
                <div class="space-y-6">
                    
                    <!-- Current Validation -->
                    <div class="bg-white rounded-2xl shadow-lg p-6" data-aos="fade-up" data-aos-delay="100">
                        <h2 class="text-xl font-bold text-gray-900 mb-6">Hasil Validasi</h2>
                        
                        <div id="validation-result" class="hidden">
                            <!-- Success Result -->
                            <div id="success-result" class="hidden">
                                <div class="text-center mb-6">
                                    <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                        <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                        </svg>
                                    </div>
                                    <h3 class="text-lg font-bold text-green-800 mb-2">Tiket Valid!</h3>
                                    <p class="text-green-600">Peserta dapat masuk ke event</p>
                                </div>
                                
                                <div class="space-y-3 text-sm">
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">Nomor Tiket:</span>
                                        <span id="ticket-number" class="font-semibold"></span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">Nama Peserta:</span>
                                        <span id="attendee-name" class="font-semibold"></span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">Event:</span>
                                        <span id="event-title" class="font-semibold"></span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">Waktu Validasi:</span>
                                        <span id="validation-time" class="font-semibold"></span>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Error Result -->
                            <div id="error-result" class="hidden">
                                <div class="text-center mb-6">
                                    <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                        <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                                        </svg>
                                    </div>
                                    <h3 class="text-lg font-bold text-red-800 mb-2">Tiket Tidak Valid!</h3>
                                    <p id="error-message" class="text-red-600"></p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Default State -->
                        <div id="default-state" class="text-center py-8">
                            <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h4M4 4h4m0 0V4m0 0h4m0 0v4M4 16h4m0 0v4m0 0h4m0 0v-4"/>
                                </svg>
                            </div>
                            <p class="text-gray-600">Scan QR code untuk memulai validasi</p>
                        </div>
                    </div>

                    <!-- Validation History -->
                    <div class="bg-white rounded-2xl shadow-lg p-6" data-aos="fade-up" data-aos-delay="200">
                        <h2 class="text-xl font-bold text-gray-900 mb-6">Riwayat Validasi</h2>
                        
                        <div id="validation-history" class="space-y-3 max-h-64 overflow-y-auto">
                            <!-- History items will be added here -->
                        </div>
                        
                        <div id="no-history" class="text-center py-8 text-gray-500">
                            Belum ada riwayat validasi
                        </div>
                    </div>

                    <!-- Statistics -->
                    <div class="bg-white rounded-2xl shadow-lg p-6" data-aos="fade-up" data-aos-delay="300">
                        <h2 class="text-xl font-bold text-gray-900 mb-6">Statistik Hari Ini</h2>
                        
                        <div class="grid grid-cols-2 gap-4">
                            <div class="text-center p-4 bg-green-50 rounded-lg">
                                <div id="valid-count" class="text-2xl font-bold text-green-600">0</div>
                                <div class="text-sm text-green-700">Tiket Valid</div>
                            </div>
                            <div class="text-center p-4 bg-red-50 rounded-lg">
                                <div id="invalid-count" class="text-2xl font-bold text-red-600">0</div>
                                <div class="text-sm text-red-700">Tiket Invalid</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/jsqr@1.4.0/dist/jsQR.js"></script>
<script>
class QRScanner {
    constructor() {
        this.video = document.getElementById('scanner-video');
        this.canvas = document.createElement('canvas');
        this.context = this.canvas.getContext('2d');
        this.scanning = false;
        this.stream = null;
        this.validCount = 0;
        this.invalidCount = 0;
        
        this.initializeElements();
        this.bindEvents();
    }
    
    initializeElements() {
        this.statusText = document.getElementById('status-text');
        this.startButton = document.getElementById('start-scanner');
        this.stopButton = document.getElementById('stop-scanner');
        this.manualForm = document.getElementById('manual-form');
        this.manualInput = document.getElementById('manual-input');
        this.validationResult = document.getElementById('validation-result');
        this.defaultState = document.getElementById('default-state');
        this.successResult = document.getElementById('success-result');
        this.errorResult = document.getElementById('error-result');
        this.validationHistory = document.getElementById('validation-history');
        this.noHistory = document.getElementById('no-history');
        this.validCountElement = document.getElementById('valid-count');
        this.invalidCountElement = document.getElementById('invalid-count');
    }
    
    bindEvents() {
        this.startButton.addEventListener('click', () => this.startScanning());
        this.stopButton.addEventListener('click', () => this.stopScanning());
        this.manualForm.addEventListener('submit', (e) => this.handleManualInput(e));
    }
    
    async startScanning() {
        try {
            this.updateStatus('Meminta akses kamera...');
            
            this.stream = await navigator.mediaDevices.getUserMedia({
                video: { 
                    facingMode: 'environment',
                    width: { ideal: 1280 },
                    height: { ideal: 720 }
                }
            });
            
            this.video.srcObject = this.stream;
            this.video.play();
            
            this.scanning = true;
            this.updateStatus('Scanner aktif - Arahkan ke QR code');
            this.startButton.disabled = true;
            this.stopButton.disabled = false;
            
            this.video.addEventListener('loadedmetadata', () => {
                this.canvas.width = this.video.videoWidth;
                this.canvas.height = this.video.videoHeight;
                this.scanFrame();
            });
            
        } catch (error) {
            console.error('Error accessing camera:', error);
            this.updateStatus('Gagal mengakses kamera');
            this.showNoCamera();
        }
    }
    
    stopScanning() {
        this.scanning = false;
        
        if (this.stream) {
            this.stream.getTracks().forEach(track => track.stop());
            this.stream = null;
        }
        
        this.video.srcObject = null;
        this.updateStatus('Scanner dihentikan');
        this.startButton.disabled = false;
        this.stopButton.disabled = true;
    }
    
    scanFrame() {
        if (!this.scanning) return;
        
        if (this.video.readyState === this.video.HAVE_ENOUGH_DATA) {
            this.context.drawImage(this.video, 0, 0, this.canvas.width, this.canvas.height);
            const imageData = this.context.getImageData(0, 0, this.canvas.width, this.canvas.height);
            const code = jsQR(imageData.data, imageData.width, imageData.height);
            
            if (code) {
                this.handleQRCode(code.data);
                return; // Stop scanning after successful read
            }
        }
        
        requestAnimationFrame(() => this.scanFrame());
    }
    
    async handleQRCode(qrData) {
        this.updateStatus('QR Code terdeteksi, memvalidasi...');
        
        try {
            // Parse QR data (assuming it's JSON)
            let ticketData;
            try {
                ticketData = JSON.parse(qrData);
            } catch {
                // If not JSON, treat as plain QR code
                ticketData = { qr_code: qrData };
            }
            
            await this.validateTicket(ticketData.qr_code || qrData);
            
        } catch (error) {
            console.error('Error handling QR code:', error);
            this.showError('Gagal memproses QR code');
        }
    }
    
    async handleManualInput(event) {
        event.preventDefault();
        const input = this.manualInput.value.trim();
        
        if (!input) {
            window.showNotification('Masukkan QR code atau nomor tiket', 'warning');
            return;
        }
        
        await this.validateTicket(input);
        this.manualInput.value = '';
    }
    
    async validateTicket(qrCode) {
        try {
            const response = await fetch('/tickets/validate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                body: JSON.stringify({ qr_code: qrCode })
            });
            
            const result = await response.json();
            
            if (response.ok && result.success) {
                this.showSuccess(result.ticket);
                this.addToHistory(result.ticket, true);
                this.validCount++;
                this.updateStats();
                
                // Play success sound
                this.playSound('success');
                
                // Vibrate if supported
                if (navigator.vibrate) {
                    navigator.vibrate([100, 50, 100]);
                }
                
            } else {
                this.showError(result.error || 'Tiket tidak valid');
                this.addToHistory({ qr_code: qrCode, error: result.error }, false);
                this.invalidCount++;
                this.updateStats();
                
                // Play error sound
                this.playSound('error');
                
                // Vibrate if supported
                if (navigator.vibrate) {
                    navigator.vibrate([200, 100, 200]);
                }
            }
            
        } catch (error) {
            console.error('Validation error:', error);
            this.showError('Gagal memvalidasi tiket. Periksa koneksi internet.');
        }
    }
    
    showSuccess(ticket) {
        this.defaultState.classList.add('hidden');
        this.errorResult.classList.add('hidden');
        this.validationResult.classList.remove('hidden');
        this.successResult.classList.remove('hidden');
        
        document.getElementById('ticket-number').textContent = ticket.ticket_number;
        document.getElementById('attendee-name').textContent = ticket.attendee_name;
        document.getElementById('event-title').textContent = ticket.event_title;
        document.getElementById('validation-time').textContent = ticket.validated_at;
        
        this.updateStatus('Tiket valid - Peserta dapat masuk');
    }
    
    showError(message) {
        this.defaultState.classList.add('hidden');
        this.successResult.classList.add('hidden');
        this.validationResult.classList.remove('hidden');
        this.errorResult.classList.remove('hidden');
        
        document.getElementById('error-message').textContent = message;
        
        this.updateStatus('Tiket tidak valid');
    }
    
    addToHistory(data, isValid) {
        const historyItem = document.createElement('div');
        historyItem.className = `flex items-center justify-between p-3 rounded-lg ${isValid ? 'bg-green-50' : 'bg-red-50'}`;
        
        const time = new Date().toLocaleTimeString('id-ID');
        const icon = isValid ? '✓' : '✗';
        const iconColor = isValid ? 'text-green-600' : 'text-red-600';
        const name = data.attendee_name || 'Unknown';
        const ticket = data.ticket_number || data.qr_code?.substring(0, 10) + '...';
        
        historyItem.innerHTML = `
            <div class="flex items-center space-x-3">
                <span class="${iconColor} font-bold">${icon}</span>
                <div>
                    <div class="font-semibold text-sm">${name}</div>
                    <div class="text-xs text-gray-600">${ticket}</div>
                </div>
            </div>
            <div class="text-xs text-gray-500">${time}</div>
        `;
        
        this.validationHistory.insertBefore(historyItem, this.validationHistory.firstChild);
        this.noHistory.classList.add('hidden');
        
        // Keep only last 10 items
        while (this.validationHistory.children.length > 10) {
            this.validationHistory.removeChild(this.validationHistory.lastChild);
        }
    }
    
    updateStats() {
        this.validCountElement.textContent = this.validCount;
        this.invalidCountElement.textContent = this.invalidCount;
    }
    
    updateStatus(message) {
        this.statusText.textContent = message;
    }
    
    showNoCamera() {
        document.getElementById('scanner-container').classList.add('hidden');
        document.getElementById('no-camera').classList.remove('hidden');
    }
    
    playSound(type) {
        // Create audio context for sound feedback
        try {
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);
            
            if (type === 'success') {
                oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
                oscillator.frequency.setValueAtTime(1000, audioContext.currentTime + 0.1);
            } else {
                oscillator.frequency.setValueAtTime(300, audioContext.currentTime);
                oscillator.frequency.setValueAtTime(200, audioContext.currentTime + 0.1);
            }
            
            gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);
            
            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.2);
        } catch (error) {
            console.log('Audio not supported');
        }
    }
}

// Initialize scanner when page loads
document.addEventListener('DOMContentLoaded', function() {
    const scanner = new QRScanner();
    
    // Auto-start scanner if camera is available
    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        scanner.startScanning();
    } else {
        scanner.showNoCamera();
    }
});
</script>
@endpush
