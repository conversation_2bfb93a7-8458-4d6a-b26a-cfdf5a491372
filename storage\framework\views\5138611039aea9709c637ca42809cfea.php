<!-- Floating Footer Navigation -->
<nav class="fixed bottom-0 left-0 right-0 bg-white/95 backdrop-blur-md border-t border-gray-200 z-50 md:hidden"
     x-data="{
        activeTab: '<?php echo e(request()->routeIs('home') ? 'home' : (request()->routeIs('tickets.*') ? 'tickets' : (request()->routeIs('organizer.*') ? 'create' : (request()->routeIs('notifications.*') ? 'notifications' : 'profile')))); ?>',
        userRole: '<?php echo e(auth()->user()->role ?? 'guest'); ?>'
     }">

    <div class="flex items-center justify-around py-2 px-4 max-w-md mx-auto">

        <!-- Home -->
        <a href="<?php echo e(route('home')); ?>"
           @click="activeTab = 'home'"
           class="flex flex-col items-center justify-center p-2 rounded-xl transition-all duration-300 min-w-0 flex-1"
           :class="activeTab === 'home' ? 'bg-primary/10 text-primary' : 'text-gray-500 hover:text-primary'">
            <div class="relative">
                <svg class="w-6 h-6 transition-transform duration-300"
                     :class="activeTab === 'home' ? 'scale-110' : ''"
                     fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"/>
                </svg>
                <!-- Active indicator -->
                <div x-show="activeTab === 'home'"
                     x-transition:enter="transition ease-out duration-200"
                     x-transition:enter-start="opacity-0 scale-0"
                     x-transition:enter-end="opacity-100 scale-100"
                     class="absolute -top-1 -right-1 w-3 h-3 bg-primary rounded-full"></div>
            </div>
            <span class="text-xs font-medium mt-1 transition-all duration-300"
                  :class="activeTab === 'home' ? 'text-primary' : ''">
                Home
            </span>
        </a>

        <!-- My Tickets -->
        <?php if(auth()->guard()->check()): ?>
        <a href="<?php echo e(route('tickets.my-tickets')); ?>"
           @click="activeTab = 'tickets'"
           class="flex flex-col items-center justify-center p-2 rounded-xl transition-all duration-300 min-w-0 flex-1"
           :class="activeTab === 'tickets' ? 'bg-primary/10 text-primary' : 'text-gray-500 hover:text-primary'">
            <div class="relative">
                <svg class="w-6 h-6 transition-transform duration-300"
                     :class="activeTab === 'tickets' ? 'scale-110' : ''"
                     fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"/>
                </svg>
                <!-- Ticket count badge -->
                <div class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold">
                    3
                </div>
                <!-- Active indicator -->
                <div x-show="activeTab === 'tickets'"
                     x-transition:enter="transition ease-out duration-200"
                     x-transition:enter-start="opacity-0 scale-0"
                     x-transition:enter-end="opacity-100 scale-100"
                     class="absolute -top-1 -right-1 w-3 h-3 bg-primary rounded-full"></div>
            </div>
            <span class="text-xs font-medium mt-1 transition-all duration-300"
                  :class="activeTab === 'tickets' ? 'text-primary' : ''">
                Tiket
            </span>
        </a>
        <?php else: ?>
        <a href="<?php echo e(route('login')); ?>"
           class="flex flex-col items-center justify-center p-2 rounded-xl transition-all duration-300 min-w-0 flex-1 text-gray-500 hover:text-primary">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"/>
            </svg>
            <span class="text-xs font-medium mt-1">Tiket</span>
        </a>
        <?php endif; ?>

        <!-- Create Event / QR Scanner (Center Button) -->
        <?php if(auth()->guard()->check()): ?>
            <?php if(auth()->user()->isPenjual() || auth()->user()->isAdmin()): ?>
                <!-- Create Event Button for Organizers -->
                <a href="#"
                   @click="activeTab = 'create'"
                   class="flex flex-col items-center justify-center p-3 rounded-2xl transition-all duration-300 transform hover:scale-105"
                   :class="activeTab === 'create' ? 'bg-gradient-to-r from-primary to-secondary text-white shadow-lg' : 'bg-primary text-white shadow-md'">
                    <div class="relative">
                        <svg class="w-7 h-7 transition-transform duration-300"
                             :class="activeTab === 'create' ? 'scale-110' : ''"
                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                        </svg>
                        <!-- Pulse animation for center button -->
                        <div class="absolute inset-0 rounded-2xl bg-primary animate-ping opacity-20"></div>
                    </div>
                    <span class="text-xs font-bold mt-1">Buat</span>
                </a>
            <?php elseif(auth()->user()->isStaff()): ?>
                <!-- QR Scanner for Staff -->
                <a href="<?php echo e(route('staff.scanner')); ?>"
                   @click="activeTab = 'scan'"
                   class="flex flex-col items-center justify-center p-3 rounded-2xl transition-all duration-300 transform hover:scale-105"
                   :class="activeTab === 'scan' ? 'bg-gradient-to-r from-primary to-secondary text-white shadow-lg' : 'bg-primary text-white shadow-md'">
                    <div class="relative">
                        <svg class="w-7 h-7 transition-transform duration-300"
                             :class="activeTab === 'scan' ? 'scale-110' : ''"
                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h4M4 4h4m0 0V4m0 0h4m0 0v4M4 16h4m0 0v4m0 0h4m0 0v-4"/>
                        </svg>
                        <!-- Pulse animation for center button -->
                        <div class="absolute inset-0 rounded-2xl bg-primary animate-ping opacity-20"></div>
                    </div>
                    <span class="text-xs font-bold mt-1">Scan</span>
                </a>
            <?php else: ?>
                <!-- Search for Regular Users -->
                <button @click="$dispatch('open-search')"
                        class="flex flex-col items-center justify-center p-3 rounded-2xl transition-all duration-300 transform hover:scale-105 bg-primary text-white shadow-md">
                    <div class="relative">
                        <svg class="w-7 h-7" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                        </svg>
                        <!-- Pulse animation for center button -->
                        <div class="absolute inset-0 rounded-2xl bg-primary animate-ping opacity-20"></div>
                    </div>
                    <span class="text-xs font-bold mt-1">Cari</span>
                </button>
            <?php endif; ?>
        <?php else: ?>
            <!-- Login prompt for guests -->
            <a href="<?php echo e(route('login')); ?>"
               class="flex flex-col items-center justify-center p-3 rounded-2xl transition-all duration-300 transform hover:scale-105 bg-primary text-white shadow-md">
                <div class="relative">
                    <svg class="w-7 h-7" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"/>
                    </svg>
                    <!-- Pulse animation for center button -->
                    <div class="absolute inset-0 rounded-2xl bg-primary animate-ping opacity-20"></div>
                </div>
                <span class="text-xs font-bold mt-1">Masuk</span>
            </a>
        <?php endif; ?>

        <!-- Notifications -->
        <?php if(auth()->guard()->check()): ?>
        <a href="#"
           @click="activeTab = 'notifications'"
           class="flex flex-col items-center justify-center p-2 rounded-xl transition-all duration-300 min-w-0 flex-1"
           :class="activeTab === 'notifications' ? 'bg-primary/10 text-primary' : 'text-gray-500 hover:text-primary'">
            <div class="relative">
                <svg class="w-6 h-6 transition-transform duration-300"
                     :class="activeTab === 'notifications' ? 'scale-110' : ''"
                     fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"/>
                </svg>
                <!-- Notification badge -->
                <div class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center font-bold">
                    2
                </div>
                <!-- Active indicator -->
                <div x-show="activeTab === 'notifications'"
                     x-transition:enter="transition ease-out duration-200"
                     x-transition:enter-start="opacity-0 scale-0"
                     x-transition:enter-end="opacity-100 scale-100"
                     class="absolute -top-1 -right-1 w-3 h-3 bg-primary rounded-full"></div>
            </div>
            <span class="text-xs font-medium mt-1 transition-all duration-300"
                  :class="activeTab === 'notifications' ? 'text-primary' : ''">
                Notif
            </span>
        </a>
        <?php else: ?>
        <a href="<?php echo e(route('register')); ?>"
           class="flex flex-col items-center justify-center p-2 rounded-xl transition-all duration-300 min-w-0 flex-1 text-gray-500 hover:text-primary">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"/>
            </svg>
            <span class="text-xs font-medium mt-1">Daftar</span>
        </a>
        <?php endif; ?>

        <!-- Profile / Account -->
        <?php if(auth()->guard()->check()): ?>
        <a href="#"
           @click="activeTab = 'profile'"
           class="flex flex-col items-center justify-center p-2 rounded-xl transition-all duration-300 min-w-0 flex-1"
           :class="activeTab === 'profile' ? 'bg-primary/10 text-primary' : 'text-gray-500 hover:text-primary'">
            <div class="relative">
                <div class="w-6 h-6 rounded-full overflow-hidden transition-transform duration-300"
                     :class="activeTab === 'profile' ? 'scale-110 ring-2 ring-primary' : ''">
                    <img src="<?php echo e(auth()->user()->avatar_url); ?>"
                         alt="<?php echo e(auth()->user()->name); ?>"
                         class="w-full h-full object-cover">
                </div>
                <!-- Active indicator -->
                <div x-show="activeTab === 'profile'"
                     x-transition:enter="transition ease-out duration-200"
                     x-transition:enter-start="opacity-0 scale-0"
                     x-transition:enter-end="opacity-100 scale-100"
                     class="absolute -top-1 -right-1 w-3 h-3 bg-primary rounded-full"></div>
            </div>
            <span class="text-xs font-medium mt-1 transition-all duration-300"
                  :class="activeTab === 'profile' ? 'text-primary' : ''">
                Akun
            </span>
        </a>
        <?php else: ?>
        <a href="<?php echo e(route('login')); ?>"
           class="flex flex-col items-center justify-center p-2 rounded-xl transition-all duration-300 min-w-0 flex-1 text-gray-500 hover:text-primary">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
            </svg>
            <span class="text-xs font-medium mt-1">Akun</span>
        </a>
        <?php endif; ?>
    </div>

    <!-- Bottom safe area for devices with home indicator -->
    <div class="h-safe-bottom bg-white/95"></div>
</nav>

<!-- Floating Action Styles -->
<style>
    /* Safe area for devices with home indicator */
    .h-safe-bottom {
        height: env(safe-area-inset-bottom, 0px);
    }

    /* Smooth transitions for all navigation elements */
    nav a, nav button {
        -webkit-tap-highlight-color: transparent;
    }

    /* Pulse animation for center button */
    @keyframes pulse-slow {
        0%, 100% {
            opacity: 0.2;
            transform: scale(1);
        }
        50% {
            opacity: 0.1;
            transform: scale(1.05);
        }
    }

    .animate-pulse-slow {
        animation: pulse-slow 2s infinite;
    }

    /* Haptic feedback simulation */
    nav a:active, nav button:active {
        transform: scale(0.95);
        transition: transform 0.1s ease;
    }

    /* Badge animations */
    .badge-bounce {
        animation: badge-bounce 2s infinite;
    }

    @keyframes badge-bounce {
        0%, 20%, 50%, 80%, 100% {
            transform: translateY(0);
        }
        40% {
            transform: translateY(-3px);
        }
        60% {
            transform: translateY(-1px);
        }
    }
</style>

<!-- JavaScript for enhanced interactions -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add haptic feedback for supported devices
    if ('vibrate' in navigator) {
        document.querySelectorAll('nav a, nav button').forEach(element => {
            element.addEventListener('touchstart', () => {
                navigator.vibrate(10); // Short vibration
            });
        });
    }

    // Update active tab based on current route
    function updateActiveTab() {
        const currentPath = window.location.pathname;
        const activeTabElement = document.querySelector('[x-data]');

        if (currentPath === '/' || currentPath === '/home') {
            activeTabElement.__x.$data.activeTab = 'home';
        } else if (currentPath.includes('/tickets')) {
            activeTabElement.__x.$data.activeTab = 'tickets';
        } else if (currentPath.includes('/notifications')) {
            activeTabElement.__x.$data.activeTab = 'notifications';
        } else if (currentPath.includes('/profile') || currentPath.includes('/account')) {
            activeTabElement.__x.$data.activeTab = 'profile';
        }
    }

    // Update on page load and navigation
    updateActiveTab();
    window.addEventListener('popstate', updateActiveTab);
});
</script>
<?php /**PATH C:\laragon\www\Project-tikpro.my.id\resources\views/layouts/partials/floating-footer.blade.php ENDPATH**/ ?>