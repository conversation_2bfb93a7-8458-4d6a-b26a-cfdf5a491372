<div>
    @if (session()->has('message'))
        <div class="bg-green-100 text-green-700 p-4 rounded-lg mb-4 flex items-center justify-between">
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
                {{ session('message') }}
            </div>
            <button wire:click="$refresh" class="text-green-700 hover:text-green-800">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                </svg>
            </button>
        </div>
    @endif

    <div class="space-y-6">
        <div class="flex items-center justify-between">
            <h3 class="text-xl font-bold">Beli Tiket</h3>
            @if($event->available_tickets <= 10)
                <span class="px-3 py-1 bg-red-100 text-red-600 rounded-full text-sm animate-pulse">
                    Sisa {{ $event->available_tickets }} tiket
                </span>
            @endif
        </div>

        <div class="space-y-4">
            <div class="flex justify-between items-center">
                <span class="text-gray-600">Harga Tiket</span>
                <div class="flex items-center space-x-2">
                    <span class="text-2xl font-bold text-primary">Rp {{ number_format($event->price, 0, ',', '.') }}</span>
                    @if($event->original_price > $event->price)
                        <span class="text-sm line-through text-gray-400">Rp {{ number_format($event->original_price, 0, ',', '.') }}</span>
                    @endif
                </div>
            </div>

            @if($event->available_tickets > 0)
                <div class="border-t border-gray-100 pt-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Jumlah Tiket</label>
                    <div class="flex items-center justify-between bg-gray-50 rounded-xl p-2">
                        <button type="button" 
                                wire:click="decrementQuantity"
                                class="w-10 h-10 rounded-full bg-white shadow-sm flex items-center justify-center text-gray-600 hover:bg-gray-50 transition-colors"
                                @if($quantity <= 1) disabled @endif>
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"/>
                            </svg>
                        </button>

                        <div class="text-center">
                            <input type="number" 
                                   wire:model="quantity"
                                   class="w-20 text-center border-0 bg-transparent text-xl font-semibold focus:ring-0"
                                   min="1"
                                   max="{{ $event->available_tickets }}">
                            <div class="text-xs text-gray-500">tiket</div>
                        </div>

                        <button type="button"
                                wire:click="incrementQuantity"
                                class="w-10 h-10 rounded-full bg-white shadow-sm flex items-center justify-center text-gray-600 hover:bg-gray-50 transition-colors"
                                @if($quantity >= $event->available_tickets) disabled @endif>
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
                            </svg>
                        </button>
                    </div>
                    @error('quantity')
                        <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div class="border-t border-gray-100 pt-4">
                    <div class="flex justify-between items-center mb-4">
                        <span class="text-gray-600">Total Pembayaran</span>
                        <span class="text-2xl font-bold text-primary">Rp {{ number_format($totalPrice, 0, ',', '.') }}</span>
                    </div>

                    <button wire:click="addToCart" 
                            class="w-full bg-primary text-white py-4 rounded-xl font-medium hover:bg-primary/90 transition-colors flex items-center justify-center space-x-2 group"
                            wire:loading.attr="disabled"
                            wire:loading.class="opacity-75">
                        <svg class="w-5 h-5 transform group-hover:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"/>
                        </svg>
                        <span wire:loading.remove>Beli Sekarang</span>
                        <span wire:loading>Memproses...</span>
                    </button>

                    <p class="mt-4 text-sm text-gray-500 text-center">
                        Dengan membeli tiket ini, Anda menyetujui syarat dan ketentuan yang berlaku
                    </p>
                </div>
            @else
                <div class="text-center py-8">
                    <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-2">Tiket Sudah Habis</h3>
                    <p class="text-gray-500">Maaf, tiket untuk event ini sudah terjual habis</p>
                </div>
            @endif
        </div>
    </div>
</div>