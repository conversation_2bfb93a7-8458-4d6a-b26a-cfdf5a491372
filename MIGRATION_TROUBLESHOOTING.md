# 🔧 TikPro Migration Troubleshooting Guide

## ❌ Error yang <PERSON>

```
SQLSTATE[HY000]: General error: 1215 Cannot add foreign key constraint 
(Connection: mysql, SQL: alter table `tickets` add constraint `tickets_order_id_foreign` 
foreign key (`order_id`) references `orders` (`id`) on delete cascade)
```

## 🔍 Analisis Masalah

### Penyebab Utama
1. **Urutan Migration Salah**: Tabel `tickets` dibuat sebelum tabel `orders`
2. **Foreign Key Dependency**: `tickets` membutuhkan `orders` yang belum ada
3. **Constraint Timing**: Laravel mencoba membuat foreign key ke tabel yang belum dibuat

### Struktur Dependency
```
users (existing)
├── categories (independent)
├── events (depends on: categories, users)
├── orders (depends on: users, events)
└── tickets (depends on: users, events, orders)
```

## ✅ Solusi yang Diterapkan

### 1. Perbaikan Urutan Migration

**Sebelum (❌ Salah):**
```
000002_create_categories_table.php
000003_create_events_table.php
000004_create_tickets_table.php    ← Salah! Sebelum orders
000005_create_orders_table.php     ← Salah! Setelah tickets
000006_create_notifications_table.php
```

**Sesudah (✅ Benar):**
```
000001_add_role_fields_to_users_table.php
000002_create_categories_table.php
000003_create_events_table.php
000004_create_orders_table.php     ← Benar! Sebelum tickets
000005_create_tickets_table.php    ← Benar! Setelah orders
000006_create_notifications_table.php
000007_fix_foreign_keys_if_needed.php (safety net)
```

### 2. Foreign Key Dependencies

#### Events Table
```php
$table->foreignId('category_id')->constrained()->onDelete('cascade');
$table->foreignId('organizer_id')->constrained('users')->onDelete('cascade');
```

#### Orders Table
```php
$table->foreignId('user_id')->constrained()->onDelete('cascade');
$table->foreignId('event_id')->constrained()->onDelete('cascade');
```

#### Tickets Table
```php
$table->foreignId('event_id')->constrained()->onDelete('cascade');
$table->foreignId('buyer_id')->constrained('users')->onDelete('cascade');
$table->foreignId('order_id')->constrained()->onDelete('cascade');  // ← Ini yang error sebelumnya
$table->foreignId('validated_by')->nullable()->constrained('users');
```

## 🚀 Cara Menjalankan Migration

### 1. Fresh Migration (Recommended)
```bash
php artisan migrate:fresh --seed
```

### 2. Step by Step (Jika ada masalah)
```bash
# Reset database
php artisan migrate:reset

# Run migrations satu per satu
php artisan migrate --path=database/migrations/2024_01_01_000001_add_role_fields_to_users_table.php
php artisan migrate --path=database/migrations/2024_01_01_000002_create_categories_table.php
php artisan migrate --path=database/migrations/2024_01_01_000003_create_events_table.php
php artisan migrate --path=database/migrations/2024_01_01_000004_create_orders_table.php
php artisan migrate --path=database/migrations/2024_01_01_000005_create_tickets_table.php
php artisan migrate --path=database/migrations/2024_01_01_000006_create_notifications_table.php

# Run seeders
php artisan db:seed
```

### 3. Testing Migration
```bash
php test-migration.php
```

## 🛡️ Safety Measures

### 1. Backup Database
```bash
mysqldump -u username -p database_name > backup_before_migration.sql
```

### 2. Check Database Engine
```sql
SHOW TABLE STATUS WHERE Engine != 'InnoDB';
```

### 3. Verify Foreign Key Support
```sql
SHOW VARIABLES LIKE 'foreign_key_checks';
```

## 🔧 Emergency Fixes

### Jika Migration Masih Gagal

1. **Disable Foreign Key Checks Sementara**
```sql
SET FOREIGN_KEY_CHECKS=0;
-- Run your migrations
SET FOREIGN_KEY_CHECKS=1;
```

2. **Manual Foreign Key Creation**
```sql
ALTER TABLE tickets 
ADD CONSTRAINT tickets_order_id_foreign 
FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE;
```

3. **Drop dan Recreate Table**
```bash
php artisan migrate:rollback --step=1
php artisan migrate
```

## ✅ Verification Checklist

- [ ] Migration files dalam urutan yang benar
- [ ] Semua referenced tables dibuat sebelum foreign keys
- [ ] Database engine adalah InnoDB
- [ ] Column types match antara parent dan child tables
- [ ] Tidak ada circular dependencies
- [ ] Foreign key constraints berhasil dibuat

## 📊 Expected Results

Setelah migration berhasil, struktur database akan seperti ini:

```
Database: tikpro
├── users (with role fields)
├── categories
├── events (FK: category_id, organizer_id)
├── orders (FK: user_id, event_id)
├── tickets (FK: event_id, buyer_id, order_id, validated_by)
└── notifications
```

## 🎯 Best Practices

1. **Selalu buat migration dalam urutan dependency**
2. **Test migration di environment development dulu**
3. **Backup database sebelum migration**
4. **Gunakan foreign key constraints untuk data integrity**
5. **Buat safety net migration untuk edge cases**

---

**Status**: ✅ **RESOLVED**  
**Last Updated**: 2024-01-01  
**Version**: TikPro v1.0
