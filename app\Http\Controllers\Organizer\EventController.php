<?php

namespace App\Http\Controllers\Organizer;

use App\Http\Controllers\Controller;
use App\Models\Event;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Intervention\Image\Facades\Image;

class EventController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'organizer']);
    }

    /**
     * Display organizer's events
     */
    public function index(Request $request)
    {
        $query = auth()->user()->organizedEvents()->with('category');

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Search
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('venue_name', 'like', "%{$search}%");
            });
        }

        $events = $query->orderBy('created_at', 'desc')
            ->paginate(10)
            ->withQueryString();

        // Get statistics
        $stats = [
            'total' => auth()->user()->organizedEvents()->count(),
            'published' => auth()->user()->organizedEvents()->where('status', 'published')->count(),
            'draft' => auth()->user()->organizedEvents()->where('status', 'draft')->count(),
            'completed' => auth()->user()->organizedEvents()->where('status', 'completed')->count(),
        ];

        return view('organizer.events.index', compact('events', 'stats'));
    }

    /**
     * Show create event form
     */
    public function create()
    {
        $categories = Category::orderBy('name')->get();
        return view('organizer.events.create', compact('categories'));
    }

    /**
     * Store new event
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'category_id' => 'required|exists:categories,id',
            'venue_name' => 'required|string|max:255',
            'venue_address' => 'required|string',
            'city' => 'required|string|max:100',
            'province' => 'required|string|max:100',
            'start_date' => 'required|date|after:now',
            'end_date' => 'required|date|after:start_date',
            'price' => 'required|numeric|min:0',
            'total_capacity' => 'required|integer|min:1',
            'poster' => 'required|image|mimes:jpeg,png,jpg|max:2048',
            'gallery.*' => 'image|mimes:jpeg,png,jpg|max:2048',
            'is_free' => 'boolean',
            'requires_approval' => 'boolean',
            'sale_start_date' => 'nullable|date|before:start_date',
            'sale_end_date' => 'nullable|date|before:start_date|after:sale_start_date',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'tags' => 'nullable|string',
        ], [
            'title.required' => 'Judul event wajib diisi.',
            'description.required' => 'Deskripsi event wajib diisi.',
            'category_id.required' => 'Kategori event wajib dipilih.',
            'venue_name.required' => 'Nama venue wajib diisi.',
            'venue_address.required' => 'Alamat venue wajib diisi.',
            'start_date.required' => 'Tanggal mulai wajib diisi.',
            'start_date.after' => 'Tanggal mulai harus setelah hari ini.',
            'end_date.required' => 'Tanggal selesai wajib diisi.',
            'end_date.after' => 'Tanggal selesai harus setelah tanggal mulai.',
            'price.required' => 'Harga tiket wajib diisi.',
            'total_capacity.required' => 'Kapasitas total wajib diisi.',
            'poster.required' => 'Poster event wajib diupload.',
            'poster.image' => 'File poster harus berupa gambar.',
            'poster.max' => 'Ukuran poster maksimal 2MB.',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        try {
            // Handle poster upload
            $posterPath = $this->uploadPoster($request->file('poster'));

            // Handle gallery upload
            $galleryPaths = [];
            if ($request->hasFile('gallery')) {
                foreach ($request->file('gallery') as $file) {
                    $galleryPaths[] = $this->uploadGalleryImage($file);
                }
            }

            // Process tags
            $tags = [];
            if ($request->filled('tags')) {
                $tags = array_map('trim', explode(',', $request->tags));
            }

            // Create event
            $event = Event::create([
                'title' => $request->title,
                'slug' => Str::slug($request->title),
                'description' => $request->description,
                'category_id' => $request->category_id,
                'organizer_id' => auth()->id(),
                'venue_name' => $request->venue_name,
                'venue_address' => $request->venue_address,
                'city' => $request->city,
                'province' => $request->province,
                'start_date' => $request->start_date,
                'end_date' => $request->end_date,
                'price' => $request->is_free ? 0 : $request->price,
                'total_capacity' => $request->total_capacity,
                'available_capacity' => $request->total_capacity,
                'poster' => $posterPath,
                'gallery' => $galleryPaths,
                'is_free' => $request->boolean('is_free'),
                'requires_approval' => $request->boolean('requires_approval'),
                'sale_start_date' => $request->sale_start_date,
                'sale_end_date' => $request->sale_end_date,
                'meta_title' => $request->meta_title,
                'meta_description' => $request->meta_description,
                'tags' => $tags,
                'status' => 'draft',
            ]);

            return redirect()->route('organizer.events.show', $event)
                ->with('success', 'Event berhasil dibuat! Silakan review dan publish event Anda.');

        } catch (\Exception $e) {
            return back()->with('error', 'Terjadi kesalahan saat membuat event: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Show event details for organizer
     */
    public function show(Event $event)
    {
        $this->authorize('view', $event);

        $event->load(['category', 'tickets.buyer']);

        // Get event statistics
        $stats = [
            'total_tickets' => $event->tickets()->count(),
            'active_tickets' => $event->tickets()->where('status', 'active')->count(),
            'used_tickets' => $event->tickets()->where('status', 'used')->count(),
            'cancelled_tickets' => $event->tickets()->where('status', 'cancelled')->count(),
            'total_revenue' => $event->tickets()->where('status', '!=', 'cancelled')->sum('total_paid'),
            'view_count' => $event->view_count ?? 0,
        ];

        return view('organizer.events.show', compact('event', 'stats'));
    }

    /**
     * Show edit form
     */
    public function edit(Event $event)
    {
        $this->authorize('update', $event);

        $categories = Category::orderBy('name')->get();
        return view('organizer.events.edit', compact('event', 'categories'));
    }

    /**
     * Update event
     */
    public function update(Request $request, Event $event)
    {
        $this->authorize('update', $event);

        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'category_id' => 'required|exists:categories,id',
            'venue_name' => 'required|string|max:255',
            'venue_address' => 'required|string',
            'city' => 'required|string|max:100',
            'province' => 'required|string|max:100',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'price' => 'required|numeric|min:0',
            'total_capacity' => 'required|integer|min:1',
            'poster' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
            'gallery.*' => 'image|mimes:jpeg,png,jpg|max:2048',
            'is_free' => 'boolean',
            'requires_approval' => 'boolean',
            'sale_start_date' => 'nullable|date|before:start_date',
            'sale_end_date' => 'nullable|date|before:start_date|after:sale_start_date',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'tags' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        try {
            $updateData = [
                'title' => $request->title,
                'slug' => Str::slug($request->title),
                'description' => $request->description,
                'category_id' => $request->category_id,
                'venue_name' => $request->venue_name,
                'venue_address' => $request->venue_address,
                'city' => $request->city,
                'province' => $request->province,
                'start_date' => $request->start_date,
                'end_date' => $request->end_date,
                'price' => $request->is_free ? 0 : $request->price,
                'total_capacity' => $request->total_capacity,
                'is_free' => $request->boolean('is_free'),
                'requires_approval' => $request->boolean('requires_approval'),
                'sale_start_date' => $request->sale_start_date,
                'sale_end_date' => $request->sale_end_date,
                'meta_title' => $request->meta_title,
                'meta_description' => $request->meta_description,
            ];

            // Handle poster upload
            if ($request->hasFile('poster')) {
                // Delete old poster
                if ($event->poster) {
                    Storage::disk('public')->delete($event->poster);
                }
                $updateData['poster'] = $this->uploadPoster($request->file('poster'));
            }

            // Handle gallery upload
            if ($request->hasFile('gallery')) {
                // Delete old gallery images
                if ($event->gallery) {
                    foreach ($event->gallery as $imagePath) {
                        Storage::disk('public')->delete($imagePath);
                    }
                }

                $galleryPaths = [];
                foreach ($request->file('gallery') as $file) {
                    $galleryPaths[] = $this->uploadGalleryImage($file);
                }
                $updateData['gallery'] = $galleryPaths;
            }

            // Process tags
            if ($request->filled('tags')) {
                $updateData['tags'] = array_map('trim', explode(',', $request->tags));
            }

            $event->update($updateData);

            return redirect()->route('organizer.events.show', $event)
                ->with('success', 'Event berhasil diperbarui!');

        } catch (\Exception $e) {
            return back()->with('error', 'Terjadi kesalahan saat memperbarui event: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Publish event
     */
    public function publish(Event $event)
    {
        $this->authorize('update', $event);

        if ($event->status !== 'draft') {
            return back()->with('error', 'Event sudah dipublish atau tidak dapat dipublish.');
        }

        $event->update(['status' => 'published']);

        return back()->with('success', 'Event berhasil dipublish dan sekarang dapat dilihat oleh publik!');
    }

    /**
     * Unpublish event
     */
    public function unpublish(Event $event)
    {
        $this->authorize('update', $event);

        if ($event->status !== 'published') {
            return back()->with('error', 'Event tidak dalam status published.');
        }

        $event->update(['status' => 'draft']);

        return back()->with('success', 'Event berhasil di-unpublish dan tidak lagi terlihat oleh publik.');
    }

    /**
     * Delete event
     */
    public function destroy(Event $event)
    {
        $this->authorize('delete', $event);

        // Check if event has tickets
        if ($event->tickets()->count() > 0) {
            return back()->with('error', 'Event tidak dapat dihapus karena sudah memiliki tiket yang terjual.');
        }

        try {
            // Delete poster and gallery images
            if ($event->poster) {
                Storage::disk('public')->delete($event->poster);
            }

            if ($event->gallery) {
                foreach ($event->gallery as $imagePath) {
                    Storage::disk('public')->delete($imagePath);
                }
            }

            $event->delete();

            return redirect()->route('organizer.events.index')
                ->with('success', 'Event berhasil dihapus.');

        } catch (\Exception $e) {
            return back()->with('error', 'Terjadi kesalahan saat menghapus event: ' . $e->getMessage());
        }
    }

    /**
     * Upload and process poster image
     */
    private function uploadPoster($file)
    {
        $filename = 'poster_' . time() . '_' . Str::random(10) . '.' . $file->getClientOriginalExtension();
        $path = 'events/posters/' . $filename;

        // Resize and optimize image
        $image = Image::make($file)
            ->resize(800, 600, function ($constraint) {
                $constraint->aspectRatio();
                $constraint->upsize();
            })
            ->encode('jpg', 85);

        Storage::disk('public')->put($path, $image);

        return $path;
    }

    /**
     * Upload and process gallery image
     */
    private function uploadGalleryImage($file)
    {
        $filename = 'gallery_' . time() . '_' . Str::random(10) . '.' . $file->getClientOriginalExtension();
        $path = 'events/gallery/' . $filename;

        // Resize and optimize image
        $image = Image::make($file)
            ->resize(1200, 800, function ($constraint) {
                $constraint->aspectRatio();
                $constraint->upsize();
            })
            ->encode('jpg', 85);

        Storage::disk('public')->put($path, $image);

        return $path;
    }
}
