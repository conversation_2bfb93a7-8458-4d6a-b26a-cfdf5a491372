<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;
use SimpleSoftwareIO\QrCode\Facades\QrCode;
use Illuminate\Support\Facades\Storage;

class Ticket extends Model
{
    use HasFactory;

    protected $fillable = [
        'ticket_number',
        'qr_code',
        'event_id',
        'buyer_id',
        'order_id',
        'attendee_name',
        'attendee_email',
        'attendee_phone',
        'price',
        'admin_fee',
        'total_paid',
        'status',
        'used_at',
        'validated_by',
        'validation_notes',
        'download_token',
        'download_count',
        'last_downloaded_at',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'admin_fee' => 'decimal:2',
        'total_paid' => 'decimal:2',
        'used_at' => 'datetime',
        'last_downloaded_at' => 'datetime',
        'download_count' => 'integer',
    ];

    /**
     * Status constants
     */
    public const STATUS_ACTIVE = 'active';
    public const STATUS_USED = 'used';
    public const STATUS_CANCELLED = 'cancelled';
    public const STATUS_REFUNDED = 'refunded';

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($ticket) {
            if (empty($ticket->ticket_number)) {
                $ticket->ticket_number = self::generateTicketNumber();
            }
            
            if (empty($ticket->qr_code)) {
                $ticket->qr_code = self::generateQrCode();
            }

            if (empty($ticket->download_token)) {
                $ticket->download_token = Str::random(32);
            }
        });
    }

    /**
     * Generate unique ticket number
     */
    public static function generateTicketNumber(): string
    {
        do {
            $number = 'TIK-' . date('Ymd') . '-' . strtoupper(Str::random(5));
        } while (self::where('ticket_number', $number)->exists());

        return $number;
    }

    /**
     * Generate unique QR code
     */
    public static function generateQrCode(): string
    {
        do {
            $qrCode = Str::random(32);
        } while (self::where('qr_code', $qrCode)->exists());

        return $qrCode;
    }

    /**
     * Event relationship
     */
    public function event(): BelongsTo
    {
        return $this->belongsTo(Event::class);
    }

    /**
     * Buyer relationship
     */
    public function buyer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'buyer_id');
    }

    /**
     * Order relationship
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Validator relationship
     */
    public function validator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'validated_by');
    }

    /**
     * Check if ticket is active
     */
    public function isActive(): bool
    {
        return $this->status === self::STATUS_ACTIVE;
    }

    /**
     * Check if ticket is used
     */
    public function isUsed(): bool
    {
        return $this->status === self::STATUS_USED;
    }

    /**
     * Check if ticket can be used
     */
    public function canBeUsed(): bool
    {
        return $this->isActive() && !$this->event->hasEnded();
    }

    /**
     * Use/validate ticket
     */
    public function use(User $validator, string $notes = null): bool
    {
        if (!$this->canBeUsed()) {
            return false;
        }

        $this->update([
            'status' => self::STATUS_USED,
            'used_at' => now(),
            'validated_by' => $validator->id,
            'validation_notes' => $notes,
        ]);

        return true;
    }

    /**
     * Generate QR code image
     */
    public function generateQrCodeImage(): string
    {
        $qrCodeData = json_encode([
            'ticket_id' => $this->id,
            'ticket_number' => $this->ticket_number,
            'qr_code' => $this->qr_code,
            'event_id' => $this->event_id,
            'verification_url' => route('tickets.verify', $this->qr_code),
        ]);

        $qrCode = QrCode::format('png')
                        ->size(300)
                        ->margin(2)
                        ->generate($qrCodeData);

        $filename = 'qr-codes/' . $this->ticket_number . '.png';
        Storage::disk('public')->put($filename, $qrCode);

        return Storage::url($filename);
    }

    /**
     * Get QR code URL
     */
    public function getQrCodeUrlAttribute(): string
    {
        $filename = 'qr-codes/' . $this->ticket_number . '.png';
        
        if (!Storage::disk('public')->exists($filename)) {
            return $this->generateQrCodeImage();
        }

        return Storage::url($filename);
    }

    /**
     * Get formatted price
     */
    public function getFormattedPriceAttribute(): string
    {
        return 'Rp ' . number_format($this->total_paid, 0, ',', '.');
    }

    /**
     * Track download
     */
    public function trackDownload(): void
    {
        $this->increment('download_count');
        $this->update(['last_downloaded_at' => now()]);
    }

    /**
     * Generate new download token
     */
    public function regenerateDownloadToken(): string
    {
        $token = Str::random(32);
        $this->update(['download_token' => $token]);
        return $token;
    }

    /**
     * Scope for active tickets
     */
    public function scopeActive($query)
    {
        return $query->where('status', self::STATUS_ACTIVE);
    }

    /**
     * Scope for used tickets
     */
    public function scopeUsed($query)
    {
        return $query->where('status', self::STATUS_USED);
    }

    /**
     * Scope for specific event
     */
    public function scopeForEvent($query, $eventId)
    {
        return $query->where('event_id', $eventId);
    }

    /**
     * Scope for specific buyer
     */
    public function scopeForBuyer($query, $buyerId)
    {
        return $query->where('buyer_id', $buyerId);
    }

    /**
     * Get route key name for model binding
     */
    public function getRouteKeyName(): string
    {
        return 'ticket_number';
    }
}
