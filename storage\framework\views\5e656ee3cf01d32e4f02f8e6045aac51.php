<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="theme-color" content="#A8D5BA">
    <title>Offline - TikPro</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #A8D5BA 0%, #C7EACB 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #2d3748;
        }
        
        .offline-container {
            text-align: center;
            padding: 2rem;
            max-width: 500px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(168, 213, 186, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .logo {
            width: 80px;
            height: 80px;
            background: #A8D5BA;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 2rem;
            box-shadow: 0 10px 25px rgba(168, 213, 186, 0.4);
        }
        
        .logo svg {
            width: 40px;
            height: 40px;
            color: white;
        }
        
        .offline-icon {
            width: 120px;
            height: 120px;
            margin: 0 auto 2rem;
            opacity: 0.7;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 0.7; }
            50% { transform: scale(1.05); opacity: 0.9; }
        }
        
        h1 {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: #2d3748;
        }
        
        p {
            font-size: 1.1rem;
            color: #4a5568;
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        
        .retry-btn {
            background: linear-gradient(135deg, #A8D5BA, #C7EACB);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(168, 213, 186, 0.4);
            margin-bottom: 1rem;
            display: inline-block;
            text-decoration: none;
        }
        
        .retry-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(168, 213, 186, 0.6);
        }
        
        .offline-features {
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #e2e8f0;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1rem;
            color: #4a5568;
        }
        
        .feature-item svg {
            width: 20px;
            height: 20px;
            margin-right: 0.5rem;
            color: #A8D5BA;
        }
        
        .connection-status {
            margin-top: 1rem;
            padding: 0.75rem;
            border-radius: 8px;
            font-size: 0.9rem;
            font-weight: 500;
        }
        
        .status-offline {
            background: #fed7d7;
            color: #c53030;
        }
        
        .status-online {
            background: #c6f6d5;
            color: #2f855a;
        }
        
        .floating-elements {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }
        
        .floating-element {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }
        
        .floating-element:nth-child(1) {
            width: 60px;
            height: 60px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }
        
        .floating-element:nth-child(2) {
            width: 40px;
            height: 40px;
            top: 60%;
            right: 15%;
            animation-delay: 2s;
        }
        
        .floating-element:nth-child(3) {
            width: 80px;
            height: 80px;
            bottom: 20%;
            left: 20%;
            animation-delay: 4s;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-20px) rotate(120deg); }
            66% { transform: translateY(20px) rotate(240deg); }
        }
        
        @media (max-width: 640px) {
            .offline-container {
                margin: 1rem;
                padding: 1.5rem;
            }
            
            h1 {
                font-size: 1.5rem;
            }
            
            p {
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Floating Background Elements -->
    <div class="floating-elements">
        <div class="floating-element"></div>
        <div class="floating-element"></div>
        <div class="floating-element"></div>
    </div>

    <div class="offline-container">
        <!-- Logo -->
        <div class="logo">
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"/>
            </svg>
        </div>

        <!-- Offline Icon -->
        <div class="offline-icon">
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" style="width: 100%; height: 100%; color: #A8D5BA;">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M18.364 5.636l-12.728 12.728m0 0L12 12m-6.364 6.364L12 12m6.364-6.364L12 12"/>
                <circle cx="12" cy="12" r="10"/>
            </svg>
        </div>

        <h1>Anda Sedang Offline</h1>
        <p>
            Tidak ada koneksi internet saat ini. Jangan khawatir, Anda masih dapat melihat tiket yang sudah dibeli dan menggunakan fitur offline TikPro.
        </p>

        <button class="retry-btn" onclick="checkConnection()">
            Coba Lagi
        </button>

        <!-- Connection Status -->
        <div id="connectionStatus" class="connection-status status-offline">
            ⚠️ Tidak ada koneksi internet
        </div>

        <!-- Offline Features -->
        <div class="offline-features">
            <h3 style="margin-bottom: 1rem; color: #2d3748;">Fitur yang Tersedia Offline:</h3>
            
            <div class="feature-item">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"/>
                </svg>
                Lihat tiket yang sudah dibeli
            </div>
            
            <div class="feature-item">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h4"/>
                </svg>
                QR Code tiket untuk verifikasi
            </div>
            
            <div class="feature-item">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                </svg>
                Detail event yang tersimpan
            </div>
        </div>
    </div>

    <script>
        // Check connection status
        function updateConnectionStatus() {
            const statusElement = document.getElementById('connectionStatus');
            
            if (navigator.onLine) {
                statusElement.textContent = '✅ Koneksi internet tersedia';
                statusElement.className = 'connection-status status-online';
            } else {
                statusElement.textContent = '⚠️ Tidak ada koneksi internet';
                statusElement.className = 'connection-status status-offline';
            }
        }

        // Check connection and redirect if online
        function checkConnection() {
            if (navigator.onLine) {
                // Try to fetch a small resource to verify connection
                fetch('/', { method: 'HEAD', cache: 'no-cache' })
                    .then(() => {
                        window.location.href = '/';
                    })
                    .catch(() => {
                        updateConnectionStatus();
                    });
            } else {
                updateConnectionStatus();
            }
        }

        // Listen for online/offline events
        window.addEventListener('online', () => {
            updateConnectionStatus();
            setTimeout(() => {
                window.location.href = '/';
            }, 1000);
        });

        window.addEventListener('offline', updateConnectionStatus);

        // Initial status check
        updateConnectionStatus();

        // Auto-check connection every 30 seconds
        setInterval(checkConnection, 30000);
    </script>
</body>
</html>
<?php /**PATH C:\laragon\www\Project-tikpro.my.id\resources\views/offline.blade.php ENDPATH**/ ?>